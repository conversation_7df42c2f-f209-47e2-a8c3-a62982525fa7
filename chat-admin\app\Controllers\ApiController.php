<?php

namespace App\Controllers;

// Подключаем автозагрузчик Composer для PHPMailer и других зависимостей
require_once __DIR__ . '/../../vendor/autoload.php';

// Используем модели и сервисы
use App\Models\ChatSession;
use App\Models\Message;
use App\Models\UploadedFile;
use App\Models\Settings; // Нужен для настроек Mistral
use App\Services\MistralApiService;
// use App\Services\TtsService; // Заменяем на EdgeTtsService
use App\Services\EdgeTtsService; // Используем новый сервис
use App\Services\ContextBuilder;
use App\Models\TextModel; // Добавляем модель для работы с текстами
use App\Services\PhoneNotificationService; // Добавляем сервис уведомлений о телефонах

class ApiController extends BaseController {

    /**
     * Возвращает URL текущей аватарки бота из настроек
     */    public function getBotAvatar(): void {
        $settingsModel = new \App\Models\Settings();
        $settings = $settingsModel->getSettings();
        $avatar = $settings['bot_avatar'] ?? '/chat-admin/images/darya.svg';

        // Преобразуем относительный путь '../chat-admin' в абсолютный '/chat-admin'
        $avatar = str_replace('../chat-admin', '/chat-admin', $avatar);

        // Если путь не начинается с '/' и не является абсолютным URL
        if (strpos($avatar, '://') === false && strpos($avatar, '/') !== 0) {
            $avatar = '/chat-admin/images/' . basename($avatar);
        }

        // Формируем полный URL
        if (strpos($avatar, '://') === false) {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
            $domain = $_SERVER['HTTP_HOST'];
            $avatar = $protocol . $domain . $avatar;
        }

        $this->sendJsonResponse(['avatar' => $avatar]);
    }

    /**
     * Возвращает настройки дизайна чата.
     */
    public function getDesignSettings(): void {
        error_log("ApiController::getDesignSettings - Start.");
        $settingsModel = new \App\Models\Settings();
        error_log("ApiController::getDesignSettings - Settings model instantiated. Fetching settings.");
        $settings = $settingsModel->getSettings();
        error_log("ApiController::getDesignSettings - Settings fetched. Result type: " . gettype($settings));

        // Если настройки не получены (например, ошибка БД или таблица пуста), возвращаем пустой массив настроек дизайна
        if ($settings === false) {
            $this->sendJsonResponse(['status' => 'error', 'message' => 'Failed to retrieve settings', 'design_settings' => []]);
            return;
        }

        // Фильтруем только настройки цветов
        $designSettings = [
            'chat_primary_color' => $settings['chat_primary_color'] ?? '#4361ee',
            'chat_primary_dark_color' => $settings['chat_primary_dark_color'] ?? '#3a56d4',
            'chat_secondary_color' => $settings['chat_secondary_color'] ?? '#3f37c9',
            'chat_text_color' => $settings['chat_text_color'] ?? '#79818f',
            'chat_text_light_color' => $settings['chat_text_light_color'] ?? '#8d99ae',
            'chat_bg_color' => $settings['chat_bg_color'] ?? '#f8f9fa',
            'chat_textarea_edited_color' => $settings['chat_textarea_edited_color'] ?? '#f8f9fa',
            'chat_card_color' => $settings['chat_card_color'] ?? '#ffffff',
            'chat_error_color' => $settings['chat_error_color'] ?? '#ef233c',
            'chat_error_gentle_color' => $settings['chat_error_gentle_color'] ?? '#ff7f50',
            'chat_error_gentle_dark_color' => $settings['chat_error_gentle_dark_color'] ?? '#e57373',
            'chat_success_color' => $settings['chat_success_color'] ?? '#4cc9f0',
            'chat_border_color' => $settings['chat_border_color'] ?? 'rgba(0, 0, 0, 0.1)',
            'chat_shadow_color' => $settings['chat_shadow_color'] ?? '0 4px 20px rgba(0, 0, 0, 0.08)',
            'chat_shadow_soft_color' => $settings['chat_shadow_soft_color'] ?? '0 8px 30px rgba(0, 0, 0, 0.1)',
            // Добавляем градиентные цвета для админки
            'chat_error_gentle_gradient_start' => $settings['chat_error_gentle_gradient_start'] ?? '#f08080',
            'chat_error_gentle_gradient_end' => $settings['chat_error_gentle_gradient_end'] ?? '#e57373',
        ];

        error_log("ApiController::getDesignSettings - Sending JSON response.");
        $this->sendJsonResponse(['status' => 'success', 'design_settings' => $designSettings]);
        error_log("ApiController::getDesignSettings - JSON response sent.");
    }

    /**
     * Генерирует динамический CSS на основе настроек
     */
    public function getDynamicCss(): void {
        header('Content-Type: text/css');
        header('Cache-Control: no-cache, must-revalidate');

        $settingsModel = new Settings();
        $settings = $settingsModel->getSettings();

        $css = ":root {\n";
        $cssVars = [
            'chat_primary_color' => '--czn-chat-primary',
            'chat_primary_dark_color' => '--czn-chat-primary-dark',
            'chat_secondary_color' => '--czn-chat-secondary',
            'chat_text_color' => '--czn-chat-text',
            'chat_text_light_color' => '--czn-chat-text-light',
            'chat_bg_color' => '--czn-chat-bg',
            'chat_card_color' => '--czn-chat-card',
            'chat_error_color' => '--czn-chat-error',
            'chat_error_gentle_color' => '--czn-chat-error-gentle',
            'chat_error_gentle_dark_color' => '--czn-chat-error-gentle-dark',
            'chat_success_color' => '--czn-chat-success',
            'chat_border_color' => '--czn-chat-border',
            'chat_shadow_color' => '--czn-chat-shadow',
            'chat_shadow_soft_color' => '--czn-chat-shadow-soft',
            'chat_error_gentle_gradient_start' => '--czn-chat-error-gentle-gradient-start',
            'chat_error_gentle_gradient_end' => '--czn-chat-error-gentle-gradient-end',
            'chat_stop_color' => '--czn-chat-stop-color'
        ];

        foreach ($cssVars as $setting => $var) {
            if (!empty($settings[$setting])) {
                $css .= "    {$var}: {$settings[$setting]};\n";
            }
        }

        $css .= "}\n";
        echo $css;
        exit;
    }

    // Добавляем константу для названия голосового чата
    private const VOICE_CHAT_TITLE = 'Голосовой чат';

    private $chatSessionModel;
    private $messageModel;
    private $uploadedFileModel;
    private $settingsModel; // Добавляем модель настроек
    private $mistralApiService;
    // private $ttsService; // Заменяем
    private $edgeTtsService; // Новый сервис
    private $contextBuilder;
    private $textModel; // Модель для работы с текстами
    private $phoneNotificationService; // Сервис уведомлений о телефонах

    private $userId = null; // ID пользователя (только админ из сессии)
    private $chatUserId = null; // ID пользователя виджета (из заголовка/GET)
    private $isAdmin = false;

    public function __construct() {
        // Инициализируем модели и сервисы
        $this->chatSessionModel = new ChatSession();
        $this->messageModel = new Message();
        $this->uploadedFileModel = new UploadedFile();
        $this->settingsModel = new Settings(); // Инициализируем модель настроек
        $this->mistralApiService = new MistralApiService();
        // $this->ttsService = new TtsService(); // Заменяем

        // Ленивая инициализация EdgeTtsService для избежания ошибок
        try {
            $this->edgeTtsService = new EdgeTtsService(); // Инициализируем новый сервис
        } catch (\Exception $e) {
            error_log("EdgeTtsService initialization failed: " . $e->getMessage());
            $this->edgeTtsService = null; // Устанавливаем null если не удалось инициализировать
        }
        $this->contextBuilder = new ContextBuilder();
        $this->textModel = new TextModel(); // Инициализируем модель для работы с текстами
        $this->phoneNotificationService = new PhoneNotificationService(); // Инициализируем сервис уведомлений

        // Определяем пользователя (админ или аноним)
        // Логика определения пользователя остается прежней
        // Запускаем сессию, если еще не запущена
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        // 1. Проверяем админа из сессии PHP
        if (isset($_SESSION['user_id'])) {
            $this->userId = $_SESSION['user_id'];
            $this->isAdmin = true;
        } else {
            // 2. Если не админ, ищем ID пользователя виджета
            $chatId = null;
            // Сначала из GET (для EventSource)
            if (isset($_GET['chat_user_id'])) {
                $chatId = $_GET['chat_user_id'];
            }
            // Потом из заголовка (для fetch)
            elseif (isset($_SERVER['HTTP_X_CHAT_USER_ID'])) {
                $chatId = $_SERVER['HTTP_X_CHAT_USER_ID'];
            }

            // Санитизация ID
            if ($chatId !== null) {
                $this->chatUserId = preg_replace('/[^a-zA-Z0-9_-]/', '', $chatId);
                if (empty($this->chatUserId)) {
                    $this->chatUserId = null; // Сбрасываем, если после санации пусто
                }
            }
            // $this->userId остается null, $this->isAdmin остается false
        }
    }

    /**
     * Обрабатывает API запрос в зависимости от action.
     * Вызывается из роутера.
     *
     * @param string $action Действие API.
     */
    public function handle(string $action): void {
        error_log("ApiController::handle - Received action: " . $action);
        // --- Streaming Actions ---
        // Handle actions that produce Server-Sent Events directly and exit
        if ($action === 'streamProxy' || $action === 'streamWithTTS') {
            try {
                // For streaming, input can come from GET (request_id, q) or POST body (less common for SSE start)
                $input = json_decode(file_get_contents('php://input'), true) ?? [];
                $input = array_merge($input, $_GET); // Merge GET params, GET takes precedence for request_id/q

                // Define public actions that don't require a logged-in user ID
                $publicActions = [
                    'createSession', 'prepareStream', 'streamProxy', 'streamWithTTS',
                    'getMessages', 'sendMessage', 'saveUserMessage', 'getActiveSession', 'synthesizeTTS',
                    'getBotAvatar', // Добавляем метод получения аватарки бота
                    'getDesignSettings', // Добавляем метод получения настроек дизайна
                    'getTextsApi', // Добавляем метод получения текстов для JS
                    'getCrmSettings', // Добавляем метод получения настроек CRM для аналитики
                    'getSettings' // Добавляем метод получения настроек для чата (без авторизации)
                    // Add others if needed by the public widget
                ];

                // Check User ID for streaming actions (allow public actions, require admin OR chat user ID otherwise)
                if (empty($this->userId) && empty($this->chatUserId) && !in_array($action, $publicActions)) {
                     throw new \Exception('User or Chat User ID is required for this action', 401);
                }

                if ($action === 'streamProxy') {
                    $this->streamProxy($input); // This method will handle output and exit
                } elseif ($action === 'streamWithTTS') {
                    $this->streamWithTTS($input); // This method will handle output and exit
                }
            } catch (\Exception $e) {
                // Log the error
                error_log("Streaming API Error: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
                // Attempt to send SSE error if headers allow
                if (!headers_sent()) {
                     header('Content-Type: text/event-stream');
                     header('Cache-Control: no-cache');
                     header('X-Accel-Buffering: no'); // For Nginx
                     // Disable buffering again just in case
                     @ini_set('output_buffering', 'off');
                     @ini_set('zlib.output_compression', false);
                     while (ob_get_level() > 0) { ob_end_flush(); }
                     ob_implicit_flush(true);
                     echo "data: " . json_encode(['type' => 'error', 'message' => 'Server error during stream: ' . $e->getMessage()]) . "\n\n";
                     flush();
                }
                exit; // Stop execution after error in streaming action
            }
            // Streaming actions exit within their methods or the catch block above
        }

        // --- Standard JSON Actions ---
        header('Content-Type: application/json'); // Set header for non-streaming actions
        $response = ['status' => 'error', 'message' => 'Unknown action'];
        $httpCode = 400;

        try {
            // Define public actions again (or define once at the top of the method)
             $publicActions = [
                 'createSession', 'prepareStream', 'streamProxy', 'streamWithTTS',
                 'getMessages', 'sendMessage', 'saveUserMessage', 'getActiveSession', 'synthesizeTTS',
                 'getBotAvatar', // Добавляем метод получения аватарки бота в список публичных действий
                 'getDesignSettings', // Добавляем метод получения настроек дизайна в список публичных действий
                 'getDynamicCss', // Добавляем метод получения динамического CSS
                 'getTextsApi', // Добавляем метод получения текстов для JS
                 'getCrmSettings', // Добавляем метод получения настроек CRM для аналитики
                 'getSettings' // Добавляем метод получения настроек для чата (без авторизации)
             ];

            // Check User ID for non-streaming actions (allow public actions, require admin OR chat user ID otherwise)
            if (empty($this->userId) && empty($this->chatUserId) && !in_array($action, $publicActions)) {
                 throw new \Exception('User or Chat User ID is required for this action', 401);
            }

            // For standard actions, prefer JSON body, merge GET as fallback
            $input = json_decode(file_get_contents('php://input'), true) ?? [];
            $input = array_merge($_GET, $input); // Body takes precedence

            error_log("ApiController::handle - Processing standard action: " . $action);
            switch ($action) {
                // --- Existing cases ---
                case 'getBotAvatar':
                    $this->getBotAvatar();
                    return; // Метод сам отправляет JSON
                case 'getDynamicCss':
                    $this->getDynamicCss();
                    return; // Метод сам отправляет CSS
                case 'getActiveSession':
                    $response = $this->getActiveSession();
                    $httpCode = 200;
                    break;
                case 'getContextParts':
                    $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
                    if (!$sessionId) throw new \Exception('Session ID is required', 400);
                    $contextParts = $this->contextBuilder->build($sessionId, 0);
                    $response = ['status' => 'success', 'context' => $contextParts];
                    $httpCode = 200;
                    break;
                case 'getFullContext':
                    $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
                    $limit = isset($input['limit']) ? intval($input['limit']) : 20;
                    if (!$sessionId) throw new \Exception('Session ID is required', 400);
                    $fullContext = $this->contextBuilder->build($sessionId, $limit);
                    $response = ['status' => 'success', 'context' => $fullContext];
                    $httpCode = 200;
                    break;
                case 'getSessions':
                    $response = $this->getUserSessions();
                    $httpCode = 200;
                    break;
                case 'createSession':
                    $response = $this->createSession($input);
                    $httpCode = 201;
                    break;
                case 'getMessages':
                    $response = $this->getMessages($input);
                    $httpCode = 200;
                    break;
                case 'sendMessage':
                    $response = $this->sendMessage($input);
                    $httpCode = 200;
                    break;
                case 'updateSession':
                    $response = $this->updateSession($input);
                    $httpCode = 200;
                    break;
                case 'updateMessage':
                    $response = $this->updateMessage($input);
                    $httpCode = 200;
                    break;
                case 'deleteMessages':
                    $response = $this->deleteMessages($input);
                    $httpCode = 200;
                    break;
                case 'deleteSession':
                    $response = $this->deleteSession($input);
                    $httpCode = 200;
                    break;
                case 'saveAssistantMessage':
                    $response = $this->saveAssistantMessage($input);
                    $httpCode = 200;
                    break;
                case 'saveUserMessage':
                    $response = $this->saveUserMessage($input);
                    $httpCode = 200;
                    break;
                case 'adminDeleteSession':
                    $this->requireAdmin();
                    $response = $this->deleteSession($input);
                    $httpCode = 200;
                    break;
                case 'voiceMessage':
                    $response = $this->handleVoiceMessage($input);
                    $httpCode = 200;
                    break;

                // --- New non-streaming actions ---
                case 'prepareStream':
                    $response = $this->prepareStream($input);
                    $httpCode = 200; // OK status for preparing stream
                    break;
                case 'synthesizeTTS':
                    $response = $this->synthesizeTTS($input);
                    // HTTP code is determined within synthesizeTTS based on success/failure
                    $httpCode = $response['status'] === 'success' ? 200 : 500;
                    break;

                case 'getDesignSettings':
                    error_log("ApiController::handle - Calling getDesignSettings.");
                    $this->getDesignSettings();
                    error_log("ApiController::handle - getDesignSettings finished.");
                    return; // Метод сам отправляет JSON

                // Методы для работы с текстами
                case 'getTextsApi':
                    $this->getTextsApi($input);
                    return; // Метод сам отправляет JSON

                // Метод для получения настроек CRM
                case 'getCrmSettings':
                    $this->getCrmSettings();
                    return; // Метод сам отправляет JSON
                    break;

                case 'initializeTexts':
                    $textsController = new \App\Controllers\TextsController();
                    $this->sendJsonResponse($textsController->initializeTexts());
                    return; // Метод сам отправляет JSON
                    break;
                case 'updateTextApi':
                    $this->sendJsonResponse($this->updateTextApi($input));
                    return; // Метод сам отправляет JSON
                    break;
                case 'getTexts':
                    $this->sendJsonResponse($this->getTexts());
                    return; // Метод сам отправляет JSON
                    break;
                case 'updateText':
                    $this->sendJsonResponse($this->updateText($input));
                    return; // Метод сам отправляет JSON
                    break;

                // Методы для работы с настройками
                case 'getSettings':
                    // Для публичного доступа возвращаем только безопасные настройки
                    $this->sendJsonResponse($this->getPublicSettings());
                    return;
                    break;
                case 'getAdminSettings':
                    // Полные настройки только для админов
                    $this->requireAdmin();
                    $this->sendJsonResponse($this->getSettings());
                    return;
                    break;
                case 'updateSettings':
                    $this->requireAdmin();
                    $this->sendJsonResponse($this->updateSettings($input));
                    return;
                    break;

                default:
                    $response = ['status' => 'error', 'message' => 'Invalid action: ' . htmlspecialchars($action)];
                    $httpCode = 400;
            }
        } catch (\Exception $e) {
            // Error handling for non-streaming actions
            $error_details = "API Error: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine();
            error_log($error_details);
            $response = ['status' => 'error', 'message' => $e->getMessage()];
            $httpCode = ($e->getCode() >= 400 && $e->getCode() < 600) ? $e->getCode() : 500;
        }

        // Output for non-streaming actions
        http_response_code($httpCode);
        echo json_encode($response);
        exit;
    }

    // ===============================================
    // Приватные методы для обработки конкретных actions
    // ===============================================

    /**
     * Возвращает ID текущего пользователя (админ или виджет).
     * @return string|int|null
     */
    private function getCurrentUserId() {
        return $this->userId ?? $this->chatUserId;
    }


    private function getActiveSession(): array {
        $currentUserId = $this->getCurrentUserId();
        if (!$currentUserId) throw new \Exception('Cannot determine user for getActiveSession', 401);
        $session = $this->chatSessionModel->getActiveSession($currentUserId);
        return ['status' => 'success', 'session_id' => $session['id'] ?? null];
    }

    private function getUserSessions(): array {
        $currentUserId = $this->getCurrentUserId();
        if (!$currentUserId) throw new \Exception('Cannot determine user for getUserSessions', 401);
        $sessions = $this->chatSessionModel->getUserSessions($currentUserId);
        return ['status' => 'success', 'sessions' => $sessions];
    }

    private function createSession(array $input): array {
        $currentUserId = $this->getCurrentUserId();

        // Для анонимных пользователей создаем специальный ID
        if ($currentUserId === null || $currentUserId === '') {
            $currentUserId = 'anonymous_' . session_id();
        }

        $title = $input['title'] ?? 'Новый чат';
        $session = $this->chatSessionModel->createSession($currentUserId, $title);
        if ($session) {
            return ['status' => 'success', 'session_id' => $session['id'], 'title' => $session['title']];
        } else {
            throw new \Exception('Failed to create session', 500);
        }
    }

    private function getMessages(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $currentUserId = $this->getCurrentUserId();
        if (!$sessionId) throw new \Exception('Invalid Session ID', 400);
        if (!$currentUserId) throw new \Exception('Cannot determine user for getMessages', 401);
        if (!$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        // Используем метод getContextMessages, так как он возвращает нужный формат
        // Лимит можно сделать параметром запроса, если нужно
        $messages = $this->messageModel->getContextMessages($sessionId, 1000); // Получаем больше истории для просмотра
        $this->chatSessionModel->touchSession($sessionId); // Обновляем время доступа
        return ['status' => 'success', 'messages' => $messages];
    }

    private function sendMessage(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageContent = trim(strip_tags($input['message'] ?? ''));
        $analyticsData = $input['analytics_data'] ?? null; // Добавляем аналитические данные
        $voiceMode = $input['context']['voice_mode'] ?? false; // Note: context might not be sent by widget
        $voiceName = $input['context']['voice_name'] ?? 'ru-RU-SvetlanaNeural'; // Голос по умолчанию
        $currentUserId = $this->getCurrentUserId();

        if (empty($messageContent)) throw new \Exception('Message content cannot be empty', 400);
        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);

        // Для анонимных пользователей используем специальный ID
        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        // Для анонимных пользователей пропускаем проверку владения сессией
        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        // Начинаем транзакцию (если модель сама не управляет)
        // $this->messageModel->db->exec('BEGIN'); // Пример, если нужно

        try {
            // 1. Сохраняем сообщение пользователя
            $userMessageId = $this->messageModel->addMessage($sessionId, 'user', $messageContent, $currentUserId);
            if (!$userMessageId) throw new \Exception('Failed to save user message', 500);

            // 1.5. НОВОЕ: Проверяем сообщение на наличие телефона и отправляем уведомление
            try {
                error_log("ApiController::sendMessage - Analytics data being passed: " . ($analyticsData ? $analyticsData : 'NULL'));
                $this->phoneNotificationService->checkAndNotify($sessionId, $messageContent, $currentUserId, $analyticsData);
            } catch (\Exception $e) {
                // Логируем ошибку, но не прерываем основной процесс
                error_log("Phone notification failed: " . $e->getMessage());
            }

            // 2. Формируем контекст
            $context = $this->contextBuilder->build($sessionId);

            // 3. Отправляем запрос к ИИ
            $reply = $this->mistralApiService->getChatCompletion($context);
            if ($reply === null) throw new \Exception('Failed to get reply from AI service', 500);

            // 4. Сохраняем ответ ассистента
            $assistantMessageId = $this->messageModel->addMessage($sessionId, 'assistant', $reply);
             if (!$assistantMessageId) throw new \Exception('Failed to save assistant message', 500);

            // 5. Обновляем сессию
            $this->chatSessionModel->touchSession($sessionId);

            // $this->messageModel->db->exec('COMMIT'); // Пример

            // Формируем базовый ответ
            $response = [
                'status' => 'success',
                'reply' => $reply,
                'user_message_id' => $userMessageId,
                'assistant_message_id' => $assistantMessageId
            ];

            // 6. Генерируем TTS, если нужно
            if ($voiceMode && $this->ttsService->isConfigured()) {
                $audioData = $this->ttsService->synthesizeSpeech($reply, $voiceName);
                if ($audioData) {
                    $response['audio'] = base64_encode($audioData);
                } else {
                     error_log("Failed to generate TTS audio for session $sessionId");
                     // Не прерываем, просто не будет аудио
                }
            }

            return $response;

        } catch (\Exception $e) {
             // $this->messageModel->db->exec('ROLLBACK'); // Пример
             throw $e; // Перебрасываем исключение
        }
    }

     private function updateSession(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $title = trim(strip_tags($input['title'] ?? ''));
        $currentUserId = $this->getCurrentUserId();

        if (empty($title)) throw new \Exception('Title is required', 400);
        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);
        if (!$currentUserId) throw new \Exception('Cannot determine user for updateSession', 401);
        if (!$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        if ($this->chatSessionModel->updateSessionTitle($sessionId, $title)) {
            return ['status' => 'success'];
        } else {
            throw new \Exception('Failed to update session title', 500);
        }
    }

    private function updateMessage(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageId = filter_var($input['message_id'] ?? null, FILTER_VALIDATE_INT);
        $newContent = trim(strip_tags($input['content'] ?? ''));
        $currentUserId = $this->getCurrentUserId();

        if (empty($newContent)) throw new \Exception('Message content is required', 400);
        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);
        if (empty($messageId)) throw new \Exception('Message ID is required', 400);
        if (!$currentUserId) throw new \Exception('Cannot determine user for updateMessage', 401);
        if (!$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }
        // TODO: Добавить проверку, что сообщение принадлежит пользователю, если нужно

        // Начинаем транзакцию
        // $this->messageModel->db->exec('BEGIN');

        try {
            // 1. Обновляем сообщение пользователя
            if (!$this->messageModel->updateMessageContent($messageId, $newContent)) {
                 throw new \Exception('Failed to update message content or message not found/not user message', 500);
            }

            // 2. Удаляем все последующие сообщения (логика из старого api.php)
            // Получаем ID всех сообщений после отредактированного, используя новый метод модели
            $idsToDelete = $this->messageModel->getSubsequentMessageIds($sessionId, $messageId);
            if (!empty($idsToDelete)) {
                $deletedCount = $this->messageModel->deleteMessagesByIds($sessionId, $idsToDelete);
                if ($deletedCount === false) {
                     error_log("Failed to delete subsequent messages during update for session $sessionId");
                     // Не прерываем, но логируем
                }
            }


            // 3. Формируем новый контекст (до отредактированного сообщения)
            // Отключено: не генерируем и не сохраняем ответ ассистента на сервере, чтобы избежать дублирования
            // $context = $this->contextBuilder->build($sessionId);

            // $reply = $this->mistralApiService->getChatCompletion($context);
            // if ($reply === null) throw new \Exception('Failed to get reply from AI service after update', 500);

            // $assistantMessageId = $this->messageModel->addMessage($sessionId, 'assistant', $reply);
            // if (!$assistantMessageId) throw new \Exception('Failed to save assistant message after update', 500);

            // 6. Обновляем сессию
            $this->chatSessionModel->touchSession($sessionId);

            // $this->messageModel->db->exec('COMMIT');

            return [
                'status' => 'success',
                'reply' => null,
                'assistant_message_id' => null,
                'edited_message_id' => $messageId
            ];

        } catch (\Exception $e) {
            // $this->messageModel->db->exec('ROLLBACK');
            throw $e;
        }
    }

    private function deleteMessages(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageIds = $input['message_ids'] ?? [];
        $currentUserId = $this->getCurrentUserId();

        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);
        if (!is_array($messageIds) || empty($messageIds)) throw new \Exception('Message IDs are required', 400);
        if (!$currentUserId) throw new \Exception('Cannot determine user for deleteMessages', 401);
        if (!$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        $deletedCount = $this->messageModel->deleteMessagesByIds($sessionId, $messageIds);

        if ($deletedCount !== false) {
            $this->chatSessionModel->touchSession($sessionId); // Обновляем время сессии
            return ['status' => 'success', 'deleted_count' => $deletedCount];
        } else {
            throw new \Exception('Failed to delete messages', 500);
        }
    }

    private function deleteSession(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? $input['id'] ?? null, FILTER_VALIDATE_INT); // Поддержка 'id' для админского удаления
        $currentUserId = $this->getCurrentUserId();

        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);

        // Админ может удалять любую сессию, обычный пользователь - только свою
        if (!$this->isAdmin) {
            if (!$currentUserId) throw new \Exception('Cannot determine user for deleteSession', 401);
            if (!$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
                 throw new \Exception('Session not found or access denied', 403);
            }
        } // Админ проходит проверку

        // Попытка удаления
        try {
            $result = $this->messageModel->deleteChatSession($sessionId); // Используем метод из Message модели

            // Считаем успехом, если не было исключения на этом уровне.
            // $result может содержать статистику или быть true/false/int в зависимости от реализации модели.
            // Главное, что операция в БД была инициирована без ошибок контроллера.
            return ['status' => 'success', 'details' => $result]; // Всегда возвращаем success, если не было исключения

        } catch (\Exception $e) {
            // Если ошибка произошла внутри deleteChatSession или на уровне БД
            error_log("Error during deleteChatSession for session $sessionId: " . $e->getMessage());
            // Перебрасываем с общим сообщением, чтобы скрыть детали реализации
            throw new \Exception('Failed to delete chat session due to an internal error', 500);
        }
    }


    /**
     * Обрабатывает запрос голосового сообщения (логика из voice_api.php).
     *
     * @param array $input Входные данные (message, context).
     * @return array Ответ для JSON.
     * @throws \Exception
     */
    private function handleVoiceMessage(array $input): array {
         // 1. Получаем ID пользователя
         $currentUserId = $this->getCurrentUserId();
         if (empty($currentUserId)) {
             throw new \Exception('User or Chat User ID could not be determined for voice message.', 400);
         }

         // 2. Извлекаем текст и параметры голоса
         $userText = trim(htmlspecialchars($input['message'] ?? '', ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8'));
         if (empty($userText)) {
             throw new \Exception('User message content cannot be empty.', 400);
         }
         $voiceShort = strtolower($input['context']['voice_name'] ?? 'svetlana');
         $voiceMapEdge = [
             'svetlana' => 'ru-RU-SvetlanaNeural',
             'dariya'   => 'ru-RU-DariyaNeural'
         ];
         $selectedEdgeVoice = $voiceMapEdge[$voiceShort] ?? $voiceMapEdge['svetlana'];
         // Параметры rate/pitch/volume будут использованы по умолчанию в EdgeTtsService

         // 3. Находим или создаем выделенную сессию голосового чата
         $voiceSession = $this->chatSessionModel->getActiveSession($currentUserId); // Проверяем последнюю сессию
         $sessionId = null;
         if ($voiceSession && $voiceSession['title'] === self::VOICE_CHAT_TITLE) {
             $sessionId = $voiceSession['id'];
             $this->chatSessionModel->touchSession($sessionId); // Обновляем время доступа
         } else {
             $newSession = $this->chatSessionModel->createSession($currentUserId, self::VOICE_CHAT_TITLE);
             if (!$newSession) {
                 throw new \Exception('Failed to create dedicated voice chat session.', 500);
             }
             $sessionId = $newSession['id'];
         }

         // Начинаем транзакцию БД (если модели сами не управляют)
         // $this->messageModel->db->exec('BEGIN');

         try {
             // 4. Сохраняем сообщение пользователя
             $userMessageId = $this->messageModel->addMessage($sessionId, 'user', $userText, $currentUserId);
             if (!$userMessageId) throw new \Exception('Failed to save user voice message', 500);

             // 5. Формируем контекст для Mistral (используя ТОЛЬКО историю этого чата и системную инструкцию)
             $mistralContext = $this->buildVoiceContext($sessionId);

             // 6. Вызываем Mistral API (используем сервис, который НЕ использует стриминг по умолчанию)
             // TODO: Модифицировать MistralApiService или создать отдельный метод для стриминга, если он нужен.
             // Пока используем стандартный getChatCompletion.
             $reply = $this->mistralApiService->getChatCompletion($mistralContext);
             if ($reply === null) throw new \Exception('Failed to get reply from AI service for voice chat', 500);

             // 7. Сохраняем ОРИГИНАЛЬНЫЙ ответ ассистента
             $assistantMessageId = $this->messageModel->addMessage($sessionId, 'assistant', $reply);
             if (!$assistantMessageId) throw new \Exception('Failed to save assistant voice message', 500);

             // 8. Синтезируем речь с помощью EdgeTtsService
             $audioBase64 = $this->edgeTtsService->synthesizeSpeech($reply, $selectedEdgeVoice);
             if ($audioBase64 === null) {
                  // Логирование ошибки произойдет внутри сервиса
                  // Не прерываем, просто не будет аудио
                  error_log("EdgeTTS synthesis failed for voice chat session $sessionId. Proceeding without audio.");
             }

             // $this->messageModel->db->exec('COMMIT');

             return [
                 'status' => 'success',
                 'audio' => $audioBase64, // Будет null, если синтез не удался
                 'reply' => $reply,       // Оригинальный ответ
                 'voice' => $selectedEdgeVoice,
                 'session_id' => $sessionId,
                 'user_message_id' => $userMessageId,
                 'assistant_message_id' => $assistantMessageId
             ];

         } catch (\Exception $e) {
              // $this->messageModel->db->exec('ROLLBACK');
              throw $e; // Перебрасываем для общей обработки ошибок API
         }
    }

    /**
     * Формирует контекст специально для голосового чата.
     * (Системная инструкция + ПОЛНАЯ история сообщений из сессии).
     *
     * @param int $sessionId ID сессии голосового чата.
     * @return array Контекст для Mistral API.
     */
    private function buildVoiceContext(int $sessionId): array {
        // Используем ContextBuilder для получения полного контекста с историей
        $context = $this->contextBuilder->build($sessionId, 9999); // Получаем всю историю

        // Модифицируем системное сообщение для голосового режима
        if (!empty($context) && $context[0]['role'] === 'system') {
            $originalSystemMessage = $context[0]['content'];
            // Добавляем голосовую инструкцию к существующему системному сообщению
            $voiceInstruction = "\n\nВажно: Тебя зовут Сара. Ты очень приятная, дружелюбная и милая девушка-ассистент. Отвечай кратко и по существу, так как общение идет голосом.";
            $context[0]['content'] = $originalSystemMessage . $voiceInstruction;
        } else {
            // Если нет системного сообщения, добавляем базовое для голоса
            $fallbackSystemMessage = "Тебя зовут Сара. Ты очень приятная, дружелюбная и милая девушка-ассистент. Всегда помни, что твое имя Сара. Если тебя спросят, как тебя зовут, ответь, что тебя зовут Сара. Будь вежливой, полезной и отвечай кратко и по существу, так как общение идет голосом.";
            array_unshift($context, ['role' => 'system', 'content' => $fallbackSystemMessage]);
        }

        // Убеждаемся, что все сообщения имеют правильный формат (role + content)
        $context = array_map(function($msg) {
            return [
                'role' => $msg['role'],
                'content' => $msg['content']
            ];
        }, $context);

        return $context;
    }


    /**
     * Проверяет, является ли текущий пользователь администратором.
     * Выбрасывает исключение, если нет.
     * @throws \Exception
     */
    protected function requireAdmin(): void {
        if (!$this->isAdmin) {
            throw new \Exception('Admin privileges required', 403); // Forbidden
        }
    }

    private function saveUserMessage(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageContent = trim(strip_tags($input['message'] ?? ''));
        $analyticsData = $input['analytics_data'] ?? null;
        $currentUserId = $this->getCurrentUserId();

        if (!$sessionId) throw new \Exception('Session ID is required', 400);
        if (!$messageContent) throw new \Exception('Message content is required', 400);

        // Для анонимных пользователей используем специальный ID
        if ($currentUserId === null || $currentUserId === '') {
            $currentUserId = 'anonymous_' . session_id(); // Используем session_id для анонимных пользователей
        }

        // Для анонимных пользователей пропускаем проверку владения сессией
        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
            throw new \Exception('Session not found or access denied', 403);
        }

        // Используем $currentUserId для добавления сообщения
        $userMessageId = $this->messageModel->addMessage($sessionId, 'user', $messageContent, $currentUserId);

        if (!$userMessageId) {
            throw new \Exception('Failed to save user message', 500);
        }

        // УДАЛЕНО: Дублирующий вызов checkAndNotify - уведомления обрабатываются только в sendMessage

        $this->chatSessionModel->touchSession($sessionId);

        // Обрабатываем CRM интеграцию, если переданы аналитические данные
        $crmResult = null;
        if ($analyticsData) {
            try {
                $crmIntegrationService = new \App\Services\CrmIntegrationService();

                // Получаем историю чата для передачи в CRM
                $chatHistory = $this->messageModel->getSessionMessages($sessionId);

                // Обрабатываем сообщение через CRM интеграцию
                $crmResult = $crmIntegrationService->processMessage($messageContent, $chatHistory, $analyticsData);

                error_log("CRM Integration result: " . json_encode($crmResult));
            } catch (\Exception $e) {
                error_log("CRM Integration error: " . $e->getMessage());
                // Не прерываем основной процесс, только логируем ошибку
            }
        }

        $response = [
            'status' => 'success',
            'user_message_id' => $userMessageId
        ];

        // Добавляем результат CRM интеграции в ответ, если он есть
        if ($crmResult) {
            $response['crm_result'] = $crmResult;
        }

        return $response;
    }

    private function saveAssistantMessage(array $input): array {
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageContent = trim($input['message'] ?? '');
        $role = $input['role'] ?? 'assistant';
        $tempId = $input['temp_id'] ?? '';

        if (!$sessionId) throw new \Exception('Session ID is required', 400);
        if (!$messageContent) throw new \Exception('Message content is required', 400);

        // Добавляем явную проверку $currentUserId перед использованием
        $currentUserId = $this->getCurrentUserId();
        if ($currentUserId === null || $currentUserId === '') {
             throw new \Exception('Cannot determine user for saveAssistantMessage (ID is null or empty)', 401);
        }

        // Используем $currentUserId для проверки владения
        if (!$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
            throw new \Exception('Session not found or access denied', 403);
        }

        // Используем $currentUserId для добавления сообщения (хотя для assistant это может быть не так важно, но для консистентности)
        $assistantMessageId = $this->messageModel->addMessage($sessionId, $role, $messageContent, $currentUserId);

        if (!$assistantMessageId) {
            throw new \Exception('Failed to save assistant message', 500);
        }

        $this->chatSessionModel->touchSession($sessionId);

        return [
            'status' => 'success',
            'assistant_message_id' => $assistantMessageId,
            'temp_id' => $tempId
        ];
    }
    // ===============================================
    // New methods for integrated functionality
    // ===============================================

    /**
     * Prepares data for a streaming request by saving it to a temporary file.
     * Replaces prepare_stream.php
     * Expects JSON payload in the POST body.
     *
     * @param array $input Input data (expected to be the payload to save).
     * @return array JSON response with status and request_id.
     * @throws \Exception If input is invalid or file saving fails.
     */
    private function prepareStream(array $input): array {
        // Use the passed input parameter instead of reading from php://input again
        $postInput = $input;
        if (empty($postInput)) {
            throw new \Exception('Empty or invalid input data for prepareStream', 400);
        }

        // Generate unique request ID
        $requestId = uniqid('req_', true);

        // Define temporary directory (consider making this configurable)
        $tmpDir = dirname(__DIR__, 2) . '/tmp'; // Use dirname(__DIR__, 2) to get chat-admin dir
        $logFile = dirname(__DIR__, 2) . '/debug_log.txt'; // Log file path

        $prepareLog = function($msg) use ($logFile) {
             $date = date('[Y-m-d H:i:s] ');
             // file_put_contents($logFile, $date . "ApiController::prepareStream - " . $msg . "\n", FILE_APPEND | LOCK_EX);
        };

        $prepareLog("Called. Request ID generated: $requestId");

        // Ensure tmp directory exists
        if (!is_dir($tmpDir)) {
            $prepareLog("Tmp directory '$tmpDir' does not exist. Attempting to create...");
            if (!mkdir($tmpDir, 0777, true) && !is_dir($tmpDir)) { // Check again after mkdir
                 $prepareLog("FAILED to create tmp directory: $tmpDir");
                 throw new \Exception('Failed to create temporary directory.', 500);
            }
            $prepareLog("Successfully created tmp directory: $tmpDir");
        } else {
             $prepareLog("Tmp directory exists: $tmpDir");
             // Check writability
             if (!is_writable($tmpDir)) {
                  $prepareLog("ERROR: Tmp directory '$tmpDir' is NOT writable!");
                  throw new \Exception("Temporary directory is not writable.", 500);
             } else {
                  $prepareLog("Tmp directory '$tmpDir' is writable.");
             }
        }

        // Build context for the session
        $sessionId = $postInput['session_id'] ?? null;
        $userMessage = $postInput['user_message'] ?? '';

        $messages = [];
        if ($sessionId) {
            $prepareLog("Building context for session $sessionId");
            $messages = $this->contextBuilder->build($sessionId, 50); // Limit history for context
        } else {
            $prepareLog("Building context without session");
            $settings = $this->settingsModel->getSettings();
            if (!empty($settings['system_message'])) {
                $messages[] = ['role' => 'system', 'content' => $settings['system_message']];
            }
        }

        // Add the current user message if provided in the payload
        if ($userMessage !== '') {
            $messages[] = ['role' => 'user', 'content' => $userMessage];
        }

        // Prepare the complete payload with context
        $completePayload = $postInput;
        $completePayload['messages'] = $messages;

        $prepareLog("Context built with " . count($messages) . " messages");

        $filePath = $tmpDir . "/stream_" . $requestId . ".json";
        $jsonData = json_encode($completePayload); // Encode the complete payload with context

        if (json_last_error() !== JSON_ERROR_NONE) {
             throw new \Exception('Failed to encode input data to JSON: ' . json_last_error_msg(), 400);
        }

        $prepareLog("Attempting to save payload to: $filePath");
        // Убираем @ для отладки ошибок записи
        $saveResult = file_put_contents($filePath, $jsonData);
        if ($saveResult === false) {
            $prepareLog("FAILED to save payload to $filePath. file_put_contents returned false.");
            // Попробуем получить последнюю ошибку PHP, если она есть
            $lastError = error_get_last();
            if ($lastError) {
                 $prepareLog("Last PHP error: " . print_r($lastError, true));
            }
            throw new \Exception('Failed to save stream payload.', 500);
        }

        $prepareLog("Successfully saved payload to $filePath, bytes: $saveResult");

        return ['status' => 'success', 'request_id' => $requestId];
    }

    /**
     * Synthesizes speech from text using EdgeTtsService.
     * Replaces tts_stream.php
     * Expects JSON payload in the POST body with 'text'.
     *
     * @param array $input Ignored (reads directly from php://input).
     * @return array JSON response with 'audio' (base64) or 'error'.
     */
    private function synthesizeTTS(array $input): array {
        $postInput = json_decode(file_get_contents('php://input'), true);
        $text = $postInput['text'] ?? '';

        if (!$text) {
            // No exception needed here, just return error status for handle()
            return ['status' => 'error', 'message' => 'No text provided in JSON body'];
        }

        try {
            $audioBase64 = $this->edgeTtsService->synthesizeSpeech($text);

            if ($audioBase64) {
                return ['status' => 'success', 'audio' => $audioBase64];
            } else {
                // Logged within the service, return error status
                return ['status' => 'error', 'message' => 'TTS synthesis failed'];
            }
        } catch (\Exception $e) {
            error_log("TTS Error in ApiController::synthesizeTTS: " . $e->getMessage());
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }


    /**
     * Handles streaming LLM responses via Server-Sent Events.
     * Replaces stream_proxy.php
     * NOTE: This method handles its own output and calls exit().
     * TODO: Requires MistralApiService to support streaming (e.g., yield chunks).
     *
     * @param array $input Input data containing 'request_id' or 'q' from GET parameters.
     */
    private function streamProxy(array $input): void {
        // --- SSE Setup ---
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('X-Accel-Buffering: no'); // For Nginx
        ignore_user_abort(true);
        @set_time_limit(0);
        @ini_set('output_buffering', 'off');
        @ini_set('zlib.output_compression', false);
        while (ob_get_level() > 0) { ob_end_flush(); }
        ob_implicit_flush(true);
        // --- End SSE Setup ---

        $logPrefix = "ApiController::streamProxy - ";
        $logFile = dirname(__DIR__, 2) . '/debug_log.txt'; // Log file path

        $proxyLog = function($msg) use ($logPrefix, $logFile) {
             $date = date('[Y-m-d H:i:s] ');
             // file_put_contents($logFile, $date . $logPrefix . $msg . "\n", FILE_APPEND | LOCK_EX);
        };

        $sendSseMessage = function(array $data) use ($proxyLog) {
            echo "data: " . json_encode($data) . "\n\n";
            flush();
            // $proxyLog("Sent SSE: " . json_encode($data)); // Optional detailed logging
        };

        $sendSseError = function(string $message, int $code = 500) use ($sendSseMessage, $proxyLog) {
             $proxyLog("Error: " . $message);
             $sendSseMessage(['type' => 'error', 'message' => $message]);
        };

        $proxyLog("called");
        $tmpFileToDelete = null; // Initialize variable

        try {
            // --- Load Payload ---
            $requestId = $input['request_id'] ?? '';
            $q = $input['q'] ?? '';
            $jsonPayload = null;

            if ($requestId) {
                $tmpDir = dirname(__DIR__, 2) . '/tmp';
                // Убираем basename(), чтобы имя файла совпадало с тем, что создается в prepareStream
                $tmpFile = $tmpDir . "/stream_" . $requestId . ".json";
                if (!file_exists($tmpFile)) {
                    throw new \Exception("Request ID not found: $requestId", 404);
                }
                $jsonPayload = file_get_contents($tmpFile);
                if ($jsonPayload === false) {
                    throw new \Exception("Failed to read request payload for ID: $requestId", 500);
                }
                $tmpFileToDelete = $tmpFile; // Mark for deletion later
                $proxyLog("Loaded payload from file: $tmpFile");
            } elseif ($q) {
                $jsonPayload = base64_decode($q);
                if ($jsonPayload === false) {
                    throw new \Exception("Invalid base64 query param", 400);
                }
                 $proxyLog("Loaded payload from query param 'q'");
            } else {
                throw new \Exception("No request_id or query param provided", 400);
            }

            $data = json_decode($jsonPayload, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Invalid JSON payload: " . json_last_error_msg(), 400);
            }
            $proxyLog("Successfully decoded payload for request ID: $requestId");
            // --- End Load Payload ---

            $sessionId = isset($data['session_id']) ? intval($data['session_id']) : 0;
            $userMessage = isset($data['user_message']) ? trim($data['user_message']) : '';

            // --- Use Cached Context or Build Fresh ---
            // Check if messages are already cached from prepareStream
            if (isset($data['messages']) && is_array($data['messages']) && !empty($data['messages'])) {
                $messages = $data['messages'];
                $proxyLog("Using cached context from prepareStream: " . count($messages) . " messages");
            } else {
                // Fallback: Build context if not cached (shouldn't happen normally)
                $proxyLog("No cached context found, building fresh context");
                $messages = [];
                if ($sessionId > 0) {
                     // Check ownership using the correct user ID
                     $currentUserId = $this->getCurrentUserId();
                     if ($currentUserId === null || $currentUserId === '') {
                          throw new \Exception('Cannot determine user for streamProxy session ownership check', 401);
                     }
                     if (!$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
                         throw new \Exception('Session not found or access denied', 403);
                     }
                     // Get context including system prompt, file context, and history
                     $proxyLog("Building context for session $sessionId");
                     $messages = $this->contextBuilder->build($sessionId, 50); // Limit history for context
                } else {
                     // Minimal context for no-session requests (e.g., system prompt only)
                     $proxyLog("Building context without session");
                     $settings = $this->settingsModel->getSettings();
                     if (!empty($settings['system_message'])) {
                         $messages[] = ['role' => 'system', 'content' => $settings['system_message']];
                     }
                }
                // Add the current user message if provided in the payload
                if ($userMessage !== '') {
                     $messages[] = ['role' => 'user', 'content' => $userMessage];
                }
            }

            if (empty($messages)) {
                 throw new \Exception("Cannot stream: No messages or context available.", 400);
            }
            $proxyLog("Context built successfully. Message count: " . count($messages));
            // --- End Build Context ---

            // --- Get API Settings ---
            $settings = $this->settingsModel->getSettings();
            $apiKey = $settings['api_key'] ?? '';
            $model = $settings['api_model'] ?? '';
            $temperature = (float)($settings['temperature'] ?? 0.7);
            $maxTokens = (int)($settings['max_tokens'] ?? 1000);

            if (empty($apiKey) || empty($model)) {
                throw new \Exception("API key or model not configured", 500);
            }
            $proxyLog("API settings loaded. Model: $model");
            // --- End Get API Settings ---

            // --- Prepare Payload for Mistral ---
             $cleanMessages = array_map(function($msg) {
                 return ['role' => $msg['role'], 'content' => $msg['content']];
             }, $messages);

             $mistralPayload = [
                 'model' => $model,
                 'messages' => $cleanMessages,
                 'temperature' => $temperature,
                 'max_tokens' => $maxTokens,
                 'stream' => true
             ];
             $proxyLog("Prepared Mistral payload: " . json_encode(array_slice($mistralPayload['messages'], -2))); // Log last 2 messages for context
            // --- End Prepare Payload ---

            // --- Decide whether to stream or not ---
            $settings = $this->settingsModel->getSettings();
            $currentApiUrl = !empty($settings['mistral_api_url']) ? trim($settings['mistral_api_url']) : MistralApiService::getDefaultApiUrl();
            $isCustomApi = $currentApiUrl !== MistralApiService::getDefaultApiUrl();
            $customApiSupportsStream = (bool)($settings['custom_api_supports_stream'] ?? false);
            $useStreaming = !$isCustomApi || ($isCustomApi && $customApiSupportsStream);
            $proxyLog("API URL: $currentApiUrl | Is Custom: " . ($isCustomApi ? 'Yes' : 'No') . " | Supports Stream: " . ($customApiSupportsStream ? 'Yes' : 'No') . " | Use Streaming: " . ($useStreaming ? 'Yes' : 'No'));
            // --- End Decision ---

            if ($useStreaming) {
                // --- Call Mistral Streaming API ---
                $proxyLog("Calling mistralApiService->streamChatCompletion...");
                $streamGenerator = $this->mistralApiService->streamChatCompletion($mistralPayload);

                $lastActivityTime = microtime(true);
                $heartbeatInterval = 15; // seconds

                $proxyLog("Iterating through stream generator...");
                $eventCounter = 0;
                foreach ($streamGenerator as $event) {
                    $eventCounter++;
                    $proxyLog("Received event #{$eventCounter} from generator: type={$event['type']}");
                    // Check for connection closed by client
                    if (connection_aborted()) {
                        $proxyLog("Client disconnected.");
                        break;
                    }

                    $currentTime = microtime(true);

                    // Send heartbeat if needed
                    if ($currentTime - $lastActivityTime > $heartbeatInterval) {
                        echo ": heartbeat\n\n";
                        flush();
                        $lastActivityTime = $currentTime;
                        $proxyLog("Sent heartbeat");
                    }

                    if ($event['type'] === 'error') {
                        $proxyLog("Generator yielded error: " . $event['message']);
                        $sendSseError("Mistral API Error: " . $event['message']);
                        break; // Stop processing on error
                    } elseif ($event['type'] === 'chunk') {
                        $sendSseMessage(['type' => 'text_chunk', 'data' => $event['data']]);
                        $lastActivityTime = $currentTime; // Update activity time on receiving data
                    } elseif ($event['type'] === 'done') {
                        $sendSseMessage(['type' => 'end']);
                        $lastActivityTime = $currentTime;
                        $proxyLog("Generator yielded 'done'. Stream finished successfully.");
                        break; // Exit loop after 'done'
                    }
                }
                // --- End Call Mistral Streaming API ---
            } else {
                // --- Call Non-Streaming API ---
                $proxyLog("Calling mistralApiService->getChatCompletion...");
                // Pass only messages, as getChatCompletion uses service's config for model/temp/etc.
                $reply = $this->mistralApiService->getChatCompletion($cleanMessages);

                if ($reply !== null) {
                    $proxyLog("Received full reply. Sending as SSE.");
                    // Send the full reply as a single chunk
                    $sendSseMessage(['type' => 'text_chunk', 'data' => $reply]);
                    // Send the end marker
                    $sendSseMessage(['type' => 'end']);
                    $proxyLog("Sent full reply and end marker.");
                } else {
                    // Handle case where getChatCompletion failed (exception should be caught below)
                    // This part might not be reached if getChatCompletion throws an exception
                    $proxyLog("getChatCompletion returned null, sending error.");
                    $sendSseError("Failed to get reply from API service.");
                }
                // --- End Call Non-Streaming API ---
            }

        } catch (\Exception $e) {
            $sendSseError($e->getMessage(), $e->getCode() >= 400 ? $e->getCode() : 500);
        } finally {
            // --- Cleanup ---
            if ($tmpFileToDelete && file_exists($tmpFileToDelete)) {
                if (unlink($tmpFileToDelete)) {
                    $proxyLog("Deleted tmp request file: $tmpFileToDelete");
                } else {
                    $proxyLog("FAILED to delete tmp request file: $tmpFileToDelete");
                }
            }
            $proxyLog("finished");
            exit; // Ensure script terminates
            // --- End Cleanup ---
        }
    }

    /**
     * Handles streaming LLM responses with integrated TTS via Server-Sent Events.
     * Replaces stream_test.php
     * NOTE: This method handles its own output and calls exit().
     * TODO: Requires MistralApiService to support streaming.
     *
     * @param array $input Input data containing 'q' (base64 encoded payload) from GET.
     */
    private function streamWithTTS(array $input): void {
        // --- SSE Setup ---
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('X-Accel-Buffering: no'); // For Nginx
        ignore_user_abort(true);
        @set_time_limit(0);
        @ini_set('output_buffering', 'off');
        @ini_set('zlib.output_compression', false);
        while (ob_get_level() > 0) { ob_end_flush(); }
        ob_implicit_flush(true);
        // --- End SSE Setup ---

        $logPrefix = "ApiController::streamWithTTS - ";
        $logFile = dirname(__DIR__, 2) . '/debug_log.txt'; // Log file path

        $ttsLog = function($msg) use ($logPrefix, $logFile) {
             $date = date('[Y-m-d H:i:s] ');
             // file_put_contents($logFile, $date . $logPrefix . $msg . "\n", FILE_APPEND | LOCK_EX);
        };

        $sendSseMessage = function(array $data) use ($ttsLog) {
            echo "data: " . json_encode($data) . "\n\n";
            flush();
             // $ttsLog("Sent SSE: " . json_encode($data)); // Optional
        };

        $sendSseError = function(string $message, int $code = 500) use ($sendSseMessage, $ttsLog) {
             $ttsLog("Error: " . $message);
             $sendSseMessage(['type' => 'error', 'message' => $message]);
        };

        $ttsLog("called");

        try {
            // --- Load Payload from 'q' ---
            $q = $input['q'] ?? '';
            if (!$q) {
                throw new \Exception("No query param 'q' provided", 400);
            }
            $jsonPayload = base64_decode($q);
            if ($jsonPayload === false) {
                throw new \Exception("Invalid base64 query param 'q'", 400);
            }
            $data = json_decode($jsonPayload, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Invalid JSON in query param 'q': " . json_last_error_msg(), 400);
            }
            $messages = $data['messages'] ?? [];
            if (empty($messages)) {
                throw new \Exception("No messages found in payload", 400);
            }
            $ttsLog("Decoded messages from 'q': " . substr($jsonPayload, 0, 200));
            // --- End Load Payload ---

            // --- Get API Settings ---
            $settings = $this->settingsModel->getSettings();
            $apiKey = $settings['api_key'] ?? '';
            $model = $settings['api_model'] ?? '';
            $temperature = (float)($settings['temperature'] ?? 0.7);
            $maxTokens = (int)($settings['max_tokens'] ?? 1000); // Note: This might be the old general max_tokens

            if (empty($apiKey) || empty($model)) {
                throw new \Exception("API key or model not configured", 500);
            }
            // --- End Get API Settings ---

            // --- Prepare Payload for API ---
             // Use mistral_max_tokens specifically if available, otherwise fallback
             $apiMaxTokens = (int)($settings['mistral_max_tokens'] ?? $maxTokens);
             $mistralPayload = [
                 'model' => $model,
                 'messages' => $messages, // Assuming messages are already clean {role, content}
                 'temperature' => $temperature,
                 'max_tokens' => $apiMaxTokens,
                 // 'stream' will be added only if streaming is used
             ];
            // --- End Prepare Payload ---

            // --- Decide whether to stream or not ---
            $currentApiUrl = !empty($settings['mistral_api_url']) ? trim($settings['mistral_api_url']) : MistralApiService::getDefaultApiUrl();
            $isCustomApi = $currentApiUrl !== MistralApiService::getDefaultApiUrl();
            $customApiSupportsStream = (bool)($settings['custom_api_supports_stream'] ?? false);
            $useStreaming = !$isCustomApi || ($isCustomApi && $customApiSupportsStream);
            $ttsLog("API URL: $currentApiUrl | Is Custom: " . ($isCustomApi ? 'Yes' : 'No') . " | Supports Stream: " . ($customApiSupportsStream ? 'Yes' : 'No') . " | Use Streaming: " . ($useStreaming ? 'Yes' : 'No'));
            // --- End Decision ---


            if ($useStreaming) {
                // --- Initialize TTS for Streaming ---
                $sentenceBuffer = '';
                $lastTtsTime = microtime(true);
                $firstAudioSent = false;
                $minWordsForFirstTTS = 1;
                $minWordsForSubsequentTTS = 2;
                $ttsTimeout = 0.25;
                // --- End Initialize TTS ---

                // --- Call Mistral Streaming API ---
                $ttsLog("Starting Mistral stream call...");
                $mistralPayload['stream'] = true; // Add stream flag for streaming call
                $streamGenerator = $this->mistralApiService->streamChatCompletion($mistralPayload);

                foreach ($streamGenerator as $event) {
                    if (connection_aborted()) {
                        $ttsLog("Client disconnected.");
                        break;
                    }

                    if ($event['type'] === 'error') {
                        $sendSseError("Mistral API Error: " . $event['message']);
                        break;
                    } elseif ($event['type'] === 'chunk') {
                        $token = $event['data'];
                        // 1. Send text chunk immediately
                        $sendSseMessage(['type' => 'text_chunk', 'data' => $token]);

                        // 2. Add to TTS buffer
                        $sentenceBuffer .= $token;
                        $trimmedBuffer = trim($sentenceBuffer);

                        // 3. Check TTS conditions
                        $shouldSynthesize = false;
                        $wordCount = empty($trimmedBuffer) ? 0 : preg_match_all('/\b\w+\b/u', $trimmedBuffer);
                        $now = microtime(true);

                        if (!$firstAudioSent && !empty($trimmedBuffer) && $wordCount >= $minWordsForFirstTTS) {
                            $ttsLog("First audio condition met (words: $wordCount >= $minWordsForFirstTTS).");
                            $shouldSynthesize = true;
                        } elseif ($firstAudioSent && !empty($trimmedBuffer)) {
                            if (preg_match('/[\.!\?…]\s*$/u', $trimmedBuffer) && $wordCount >= $minWordsForSubsequentTTS) {
                                $ttsLog("Subsequent audio condition met (sentence end, words: $wordCount >= $minWordsForSubsequentTTS).");
                                $shouldSynthesize = true;
                            } elseif ($wordCount >= $minWordsForSubsequentTTS && ($now - $lastTtsTime) > $ttsTimeout) {
                                $ttsLog("Subsequent audio condition met (timeout: " . round($now - $lastTtsTime, 2) . "s > $ttsTimeout" . "s, words: $wordCount >= $minWordsForSubsequentTTS).");
                                $shouldSynthesize = true;
                            }
                        }

                        // 4. Synthesize and send audio if needed
                        if ($shouldSynthesize) {
                            $ttsLog("Calling TTS for: " . $trimmedBuffer);
                            try {
                                $audioBase64 = $this->edgeTtsService->synthesizeSpeech($trimmedBuffer);
                                if ($audioBase64) {
                                    $sendSseMessage(['type' => 'audio_chunk', 'data' => $audioBase64]);
                                    $ttsLog("Sent audio chunk.");
                                    $sentenceBuffer = ''; // Clear buffer
                                    $lastTtsTime = microtime(true);
                                    $firstAudioSent = true;
                                } else {
                                    $ttsLog("TTS returned empty for buffer: " . $trimmedBuffer);
                                }
                            } catch (\Exception $e) {
                                $ttsLog("TTS Error: " . $e->getMessage() . " | Buffer: " . $trimmedBuffer);
                            }
                        }

                    } elseif ($event['type'] === 'done') {
                        $ttsLog("[DONE] received from Mistral.");
                        // Send final TTS buffer if any
                        $trimmedBuffer = trim($sentenceBuffer);
                        if (!empty($trimmedBuffer)) {
                            $ttsLog("Sending final buffer to TTS: " . $trimmedBuffer);
                            try {
                                $audioBase64 = $this->edgeTtsService->synthesizeSpeech($trimmedBuffer);
                                if ($audioBase64) {
                                    $sendSseMessage(['type' => 'audio_chunk', 'data' => $audioBase64]);
                                    $ttsLog("Sent final audio chunk.");
                                } else {
                                    $ttsLog("TTS returned empty for final buffer.");
                                }
                            } catch (\Exception $e) {
                                $ttsLog("TTS Error (final buffer): " . $e->getMessage());
                            }
                        }
                        // Send end marker
                        $sendSseMessage(['type' => 'end']);
                        $ttsLog("Sent [DONE] marker to client.");
                        break; // Exit loop
                    }
                }
                // --- End Call Mistral Streaming API ---
            } else {
                 // --- Call Non-Streaming API with TTS ---
                 $ttsLog("Calling non-streaming API (getChatCompletion)...");
                 // Pass only messages to getChatCompletion
                 $reply = $this->mistralApiService->getChatCompletion($mistralPayload['messages']);

                 if ($reply !== null) {
                     $ttsLog("Received full reply. Sending text and synthesizing full audio.");
                     // 1. Send the full text reply
                     $sendSseMessage(['type' => 'text_chunk', 'data' => $reply]);

                     // 2. Synthesize the entire reply
                     try {
                         $ttsLog("Calling TTS for full reply...");
                         $audioBase64 = $this->edgeTtsService->synthesizeSpeech($reply);
                         if ($audioBase64) {
                             $sendSseMessage(['type' => 'audio_chunk', 'data' => $audioBase64]);
                             $ttsLog("Sent full audio chunk.");
                         } else {
                             $ttsLog("TTS returned empty for full reply.");
                         }
                     } catch (\Exception $e) {
                         $ttsLog("TTS Error (full reply): " . $e->getMessage());
                         // Don't send error to client for TTS failure, just log it
                     }

                     // 3. Send the end marker
                     $sendSseMessage(['type' => 'end']);
                     $ttsLog("Sent end marker after full reply.");

                 } else {
                     // Handle case where getChatCompletion failed
                     $ttsLog("getChatCompletion returned null, sending error.");
                     $sendSseError("Failed to get reply from API service.");
                 }
                 // --- End Call Non-Streaming API with TTS ---
            }

        } catch (\Exception $e) {
            $sendSseError($e->getMessage(), $e->getCode() >= 400 ? $e->getCode() : 500);
        } finally {
            $ttsLog("finished");
            exit; // Ensure script terminates
        }
    }

    /**
     * Получает все тексты для JS-интерфейса.
     * Используется в textManager.js для загрузки текстов.
     *
     * @param array $input Параметры запроса (lang - язык текстов).
     */
    private function getTextsApi(array $input): void {
        $language = $input['lang'] ?? 'ru';
        $texts = $this->textModel->getAllTextsForJs($language);

        if (empty($texts)) {
            $this->sendJsonResponse(['status' => 'error', 'message' => 'No texts found']);
            exit;
        }

        // Возвращаем тексты в формате, который ожидает textManager.js
        $this->sendJsonResponse($texts);
        exit;
    }

    /**
     * Обновляет текст по ключу и генерирует JS-файл с текстами.
     *
     * @param array $input Параметры запроса (key - ключ текста, text - новое значение, lang - язык).
     * @return array Результат операции.
     */
    private function updateTextApi(array $input): array {
        $key = $input['key'] ?? '';
        $text = $input['text'] ?? '';
        $language = $input['lang'] ?? 'ru';

        if (empty($key) || empty($text)) {
            return ['status' => 'error', 'message' => 'Key and text are required'];
        }

        $success = $this->textModel->updateText($key, $text, $language);

        if ($success) {
            // Генерируем JS-файл с текстами
            $this->generateTextsJsFile($language);
            return ['status' => 'success', 'message' => 'Text updated successfully'];
        } else {
            return ['status' => 'error', 'message' => 'Failed to update text'];
        }
    }

    /**
     * Получает все тексты для админки.
     * Используется в settings_texts.php для отображения списка текстов.
     *
     * @return array Массив с текстами для админки.
     */
    private function getTexts(): array {
        // Проверка авторизации уже выполняется в settings.php, поэтому здесь не требуется
        // $this->requireAdmin(); // Только для админа

        $texts = [];
        $result = $this->textModel->getAllTexts();

        if (!empty($result)) {
            foreach ($result as $item) {
                $texts[] = [
                    'id' => $item['id'],
                    'text_key' => $item['key'],
                    'text_value' => $item['text'],
                    'language' => $item['language']
                ];
            }
        }

        return ['status' => 'success', 'texts' => $texts];
    }

    /**
     * Обновляет текст по ID и генерирует JS-файл с текстами.
     * Используется в settings_texts.php для сохранения изменений.
     *
     * @param array $input Параметры запроса (id - ID текста, content - новое значение).
     * @return array Результат операции.
     */
    private function updateText(array $input): array {
        // Проверка авторизации уже выполняется в settings.php, поэтому здесь не требуется
        // $this->requireAdmin(); // Только для админа

        $postInput = json_decode(file_get_contents('php://input'), true) ?? [];
        $id = $postInput['id'] ?? null;
        $content = $postInput['content'] ?? '';

        if (!$id || $content === '') {
            return ['status' => 'error', 'message' => 'ID and content are required'];
        }

        $success = $this->textModel->updateTextById((int)$id, $content);

        if ($success) {
            // Получаем язык текста
            $textInfo = $this->textModel->getTextById((int)$id);
            if ($textInfo) {
                $language = $textInfo['language'] ?? 'ru';
                // Генерируем JS-файл с текстами
                $this->generateTextsJsFile($language);
            }

            return ['status' => 'success', 'message' => 'Text updated successfully'];
        } else {
            return ['status' => 'error', 'message' => 'Failed to update text'];
        }
    }

    /**
     * Генерирует JS-файл с текстами для указанного языка.
     *
     * @param string $language Язык текстов.
     * @return bool Результат операции.
     */
    private function generateTextsJsFile(string $language = 'ru'): bool {
        $texts = $this->textModel->getAllTextsForJs($language);

        if (empty($texts)) {
            error_log("No texts found for language: $language");
            return false;
        }

        // Формируем содержимое JS-файла
        $jsContent = "/**\n";
        $jsContent .= " * Автоматически сгенерированный файл с текстами для языка: $language\n";
        $jsContent .= " * Дата генерации: " . date('Y-m-d H:i:s') . "\n";
        $jsContent .= " */\n\n";
        $jsContent .= "export const texts = " . json_encode($texts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . ";\n";

        // Путь к файлу
        $filePath = __DIR__ . '/../../chat-js/texts_' . $language . '.js';

        // Сохраняем файл
        $result = file_put_contents($filePath, $jsContent);

        if ($result === false) {
            error_log("Failed to write texts JS file: $filePath");
            return false;
        }

        error_log("Texts JS file generated successfully: $filePath");
        return true;
    }

    /**
     * Получает публичные настройки для чата (без чувствительной информации)
     */
    private function getPublicSettings(): array {
        try {
            $settings = $this->settingsModel->getSettings();

            // Получаем превью сообщения из таблицы texts
            $textModel = new \App\Models\TextModel($this->db);
            $previewMessage1 = $textModel->getTextByKey('preview_message_1');
            $previewMessage2 = $textModel->getTextByKey('preview_message_2');

            return [
                'status' => 'success',
                'settings' => [
                    'preview_messages_enabled' => $settings['preview_messages_enabled'] ?? 1,
                    'preview_message_delay' => $settings['preview_message_delay'] ?? 3000,
                    'preview_hide_greeting' => $settings['preview_hide_greeting'] ?? 1,
                    'auto_open_chat' => $settings['auto_open_chat'] ?? 1,
                    'auto_open_delay' => $settings['auto_open_delay'] ?? 0,
                    'preview_message_1' => $previewMessage1 ? $previewMessage1['text'] : '',
                    'preview_message_2' => $previewMessage2 ? $previewMessage2['text'] : ''
                    // Не включаем чувствительные данные: email настройки, SMTP пароли и т.д.
                ]
            ];
        } catch (\Exception $e) {
            error_log("Error getting public settings: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => 'Failed to get settings'
            ];
        }
    }

    /**
     * Получает полные настройки (только для админов)
     */
    private function getSettings(): array {
        try {
            $settings = $this->settingsModel->getSettings();

            return [
                'status' => 'success',
                'settings' => [
                    'preview_messages_enabled' => $settings['preview_messages_enabled'] ?? 1,
                    'preview_message_delay' => $settings['preview_message_delay'] ?? 3000,
                    'preview_hide_greeting' => $settings['preview_hide_greeting'] ?? 1,
                    'auto_open_chat' => $settings['auto_open_chat'] ?? 1,
                    'auto_open_delay' => $settings['auto_open_delay'] ?? 0,
                    'notification_email' => $settings['notification_email'] ?? '',
                    'email_from' => $settings['email_from'] ?? '<EMAIL>',
                    'email_subject' => $settings['email_subject'] ?? 'Новый телефон из чата: {phone}',
                    'email_body' => $settings['email_body'] ?? 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}',
                    'smtp_enabled' => $settings['smtp_enabled'] ?? 0,
                    'smtp_host' => $settings['smtp_host'] ?? '',
                    'smtp_port' => $settings['smtp_port'] ?? 587,
                    'smtp_username' => $settings['smtp_username'] ?? '',
                    'smtp_password' => $settings['smtp_password'] ?? '',
                    'smtp_encryption' => $settings['smtp_encryption'] ?? 'tls'
                ]
            ];
        } catch (\Exception $e) {
            error_log("Error getting settings: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => 'Failed to get settings'
            ];
        }
    }

    /**
     * Обновляет настройки превью-сообщений
     */
    private function updateSettings(array $input): array {
        try {
            $allowedSettings = ['preview_messages_enabled', 'preview_message_delay', 'preview_hide_greeting', 'auto_open_chat', 'auto_open_delay', 'notification_email', 'email_from', 'email_subject', 'email_body', 'smtp_enabled', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption'];
            $updates = [];

            foreach ($allowedSettings as $setting) {
                if (isset($input[$setting])) {
                    $updates[$setting] = $input[$setting];
                }
            }

            if (empty($updates)) {
                return [
                    'status' => 'error',
                    'message' => 'No valid settings provided'
                ];
            }

            // Валидация
            if (isset($updates['preview_message_delay'])) {
                $delay = intval($updates['preview_message_delay']);
                if ($delay < 1000 || $delay > 10000) {
                    return [
                        'status' => 'error',
                        'message' => 'Preview delay must be between 1000 and 10000 ms'
                    ];
                }
                $updates['preview_message_delay'] = $delay;
            }

            if (isset($updates['auto_open_delay'])) {
                $delay = intval($updates['auto_open_delay']);
                if ($delay < 0 || $delay > 30000) {
                    return [
                        'status' => 'error',
                        'message' => 'Auto open delay must be between 0 and 30000 ms'
                    ];
                }
                $updates['auto_open_delay'] = $delay;
            }

            // Обновляем настройки
            foreach ($updates as $key => $value) {
                $this->settingsModel->updateSetting($key, $value);
            }

            return [
                'status' => 'success',
                'message' => 'Settings updated successfully'
            ];

        } catch (\Exception $e) {
            error_log("Error updating settings: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => 'Failed to update settings'
            ];
        }
    }

    /**
     * Получает настройки CRM для диагностики
     */
    private function getCrmSettings(): void
    {
        try {
            $crmSettings = new \App\Models\CrmSettings();
            $settings = $crmSettings->getSettings();

            // Скрываем чувствительные данные
            if (isset($settings['amocrm_client_secret'])) {
                $settings['amocrm_client_secret'] = '***HIDDEN***';
            }
            if (isset($settings['amocrm_password'])) {
                $settings['amocrm_password'] = '***HIDDEN***';
            }

            header('Content-Type: application/json; charset=utf-8');
            echo json_encode([
                'success' => true,
                'settings' => $settings
            ], JSON_UNESCAPED_UNICODE);

        } catch (\Exception $e) {
            error_log("Error getting CRM settings: " . $e->getMessage());
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode([
                'success' => false,
                'message' => 'Failed to get CRM settings: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
}

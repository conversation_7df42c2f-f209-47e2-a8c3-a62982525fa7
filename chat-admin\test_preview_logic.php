<?php
declare(strict_types=1);

echo "🧪 ТЕСТ: Логика показа превью сообщений\n";
echo "======================================\n\n";

echo "🎯 ПРАВИЛА ПОКАЗА ПРЕВЬЮ:\n";
echo "=========================\n";
echo "✅ Показывать превью ТОЛЬКО если:\n";
echo "   1. В DOM меньше 2 превью сообщений\n";
echo "   2. История сообщений пуста (messageHistory.length = 0)\n";
echo "   3. В DOM нет НЕ-превью сообщений\n";
echo "   4. Настроены превью тексты в админке\n";
echo "   5. Чат открыт\n\n";

echo "❌ НЕ показывать превью если:\n";
echo "   1. В DOM уже есть 2+ превью сообщения\n";
echo "   2. В истории есть сообщения (messageHistory.length > 0)\n";
echo "   3. В DOM есть реальные сообщения (НЕ-превью)\n";
echo "   4. Не настроены превью тексты\n\n";

echo "🔍 ЛОГИКА ПРОВЕРКИ В КОДЕ:\n";
echo "==========================\n\n";

echo "1. ПЕРВАЯ ПРОВЕРКА - Дублирование превью:\n";
echo "```javascript\n";
echo "const existingPreviewMessages = chatMessages ? \n";
echo "    chatMessages.querySelectorAll('.czn-chat-message-preview') : [];\n";
echo "if (existingPreviewMessages.length >= 2) {\n";
echo "    console.log('Already have 2 preview messages, skipping');\n";
echo "    return;\n";
echo "}\n";
echo "```\n\n";

echo "2. ВТОРАЯ ПРОВЕРКА - История сообщений:\n";
echo "```javascript\n";
echo "const messageHistory = getState().messageHistory || [];\n";
echo "console.log('History length:', messageHistory.length);\n";
echo "```\n\n";

echo "3. ТРЕТЬЯ ПРОВЕРКА - Реальные сообщения в DOM:\n";
echo "```javascript\n";
echo "const nonPreviewMessages = chatMessages ? \n";
echo "    chatMessages.querySelectorAll('.czn-chat-message:not(.czn-chat-message-preview)') : [];\n";
echo "console.log('Non-preview DOM messages count:', nonPreviewMessages.length);\n";
echo "```\n\n";

echo "4. ОСНОВНАЯ ПРОВЕРКА:\n";
echo "```javascript\n";
echo "if (messageHistory.length > 0 || nonPreviewMessages.length > 0) {\n";
echo "    console.log('Has messages in history or non-preview DOM messages, no preview');\n";
echo "    return;\n";
echo "}\n";
echo "```\n\n";

echo "🧪 СЦЕНАРИИ ТЕСТИРОВАНИЯ:\n";
echo "=========================\n\n";

echo "СЦЕНАРИЙ 1: Пустой чат (должны показаться превью)\n";
echo "- messageHistory.length = 0\n";
echo "- nonPreviewMessages.length = 0\n";
echo "- existingPreviewMessages.length < 2\n";
echo "- Результат: ✅ Превью показываются\n\n";

echo "СЦЕНАРИЙ 2: Есть история сообщений (превью НЕ должны показаться)\n";
echo "- messageHistory.length > 0\n";
echo "- nonPreviewMessages.length = любое\n";
echo "- Результат: ❌ Превью НЕ показываются\n\n";

echo "СЦЕНАРИЙ 3: Есть реальные сообщения в DOM (превью НЕ должны показаться)\n";
echo "- messageHistory.length = 0\n";
echo "- nonPreviewMessages.length > 0\n";
echo "- Результат: ❌ Превью НЕ показываются\n\n";

echo "СЦЕНАРИЙ 4: Уже есть 2 превью (новые превью НЕ должны добавиться)\n";
echo "- existingPreviewMessages.length >= 2\n";
echo "- Результат: ❌ Новые превью НЕ добавляются\n\n";

echo "🔧 ИСПРАВЛЕНИЯ:\n";
echo "===============\n";
echo "1. ✅ Убран дублированный вызов checkAndShowPreviewForEmptyChat()\n";
echo "2. ✅ Добавлено подробное логирование для отладки\n";
echo "3. ✅ Исправлена ошибка с \$db в ApiController\n\n";

echo "📊 КАК ТЕСТИРОВАТЬ:\n";
echo "===================\n";
echo "1. Откройте консоль браузера (F12)\n";
echo "2. Удалите все чаты\n";
echo "3. Перезагрузите страницу\n";
echo "4. Смотрите логи:\n\n";

echo "ОЖИДАЕМЫЕ ЛОГИ ДЛЯ ПУСТОГО ЧАТА:\n";
echo "```\n";
echo "UIChat: checkAndShowPreviewForEmptyChat called!\n";
echo "UIChat: Existing preview messages in DOM: 0\n";
echo "UIChat: History length: 0\n";
echo "UIChat: Non-preview DOM messages count: 0\n";
echo "UIChat: Empty chat (both history and DOM) - showing preview WITHOUT saving!\n";
echo "UIChat: Preview message 1 shown (not saved to database)\n";
echo "UIChat: Preview message 2 shown (not saved to database)\n";
echo "```\n\n";

echo "ОЖИДАЕМЫЕ ЛОГИ ДЛЯ ЧАТА С СООБЩЕНИЯМИ:\n";
echo "```\n";
echo "UIChat: checkAndShowPreviewForEmptyChat called!\n";
echo "UIChat: Existing preview messages in DOM: 0\n";
echo "UIChat: History length: 3 (или > 0)\n";
echo "UIChat: Non-preview DOM messages count: 2 (или > 0)\n";
echo "UIChat: Has messages in history or non-preview DOM messages, no preview\n";
echo "UIChat: Reason - messageHistory.length: 3 nonPreviewMessages.length: 2\n";
echo "```\n\n";

echo "🎯 РЕЗУЛЬТАТ:\n";
echo "=============\n";
echo "Теперь превью сообщения:\n";
echo "✅ Показываются ТОЛЬКО в пустых чатах\n";
echo "✅ НЕ показываются если есть реальные сообщения\n";
echo "✅ НЕ дублируются (3 уровня защиты)\n";
echo "✅ Имеют подробное логирование для отладки\n";
echo "✅ Работают корректно во всех сценариях\n";
?>

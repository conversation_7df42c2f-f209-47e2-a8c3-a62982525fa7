<?php
// Простая инициализация превью текстов
echo "Инициализация превью текстов...\n";

try {
    // Подключение к базе данных
    $db = new SQLite3('database/app.sqlite');
    
    // Проверяем существующие тексты
    $stmt = $db->prepare("SELECT * FROM texts WHERE key IN ('preview_message_1', 'preview_message_2')");
    $result = $stmt->execute();
    
    $existing = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $existing[$row['key']] = $row['text'];
    }
    
    echo "Найдено существующих превью: " . count($existing) . "\n";
    
    // Тексты для добавления
    $texts = [
        'preview_message_1' => 'Здравствуйте. Скажите, вам ясно, кто виноват в заливе - соседи или УК?',
        'preview_message_2' => 'Главное получить корректный Акт о заливе, рассказать вам, как он должен выглядеть?'
    ];
    
    foreach ($texts as $key => $text) {
        if (!isset($existing[$key])) {
            $stmt = $db->prepare("INSERT INTO texts (key, text) VALUES (?, ?)");
            $stmt->bindValue(1, $key, SQLITE3_TEXT);
            $stmt->bindValue(2, $text, SQLITE3_TEXT);
            $result = $stmt->execute();
            
            if ($result) {
                echo "✅ Добавлен: $key\n";
            } else {
                echo "❌ Ошибка добавления: $key\n";
            }
        } else {
            echo "ℹ️ Уже существует: $key = " . $existing[$key] . "\n";
        }
    }
    
    // Проверяем результат
    echo "\nПроверка результата:\n";
    $stmt = $db->prepare("SELECT * FROM texts WHERE key IN ('preview_message_1', 'preview_message_2')");
    $result = $stmt->execute();
    
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        echo "✓ " . $row['key'] . ": " . $row['text'] . "\n";
    }
    
    $db->close();
    echo "\n🎉 Инициализация завершена!\n";
    
} catch (Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
}
?>

<?php
declare(strict_types=1);

require_once 'app/Models/Database.php';
require_once 'app/Models/TextModel.php';

use App\Models\Database;
use App\Models\TextModel;

try {
    $db = Database::getInstance();
    $textModel = new TextModel($db);
    
    echo "Проверка превью текстов в базе данных:\n";
    echo "=====================================\n";
    
    $preview1 = $textModel->getTextByKey('preview_message_1');
    $preview2 = $textModel->getTextByKey('preview_message_2');
    
    echo "preview_message_1: ";
    if ($preview1) {
        echo "НАЙДЕН - " . $preview1['text'] . "\n";
    } else {
        echo "НЕ НАЙДЕН!\n";
    }
    
    echo "preview_message_2: ";
    if ($preview2) {
        echo "НАЙДЕН - " . $preview2['text'] . "\n";
    } else {
        echo "НЕ НАЙДЕН!\n";
    }
    
    // Если не найдены - добавляем
    if (!$preview1) {
        $textModel->addText('preview_message_1', 'Здравствуйте. Скажите, вам ясно, кто виноват в заливе - соседи или УК?');
        echo "ДОБАВЛЕН preview_message_1\n";
    }
    
    if (!$preview2) {
        $textModel->addText('preview_message_2', 'Главное получить корректный Акт о заливе, рассказать вам, как он должен выглядеть?');
        echo "ДОБАВЛЕН preview_message_2\n";
    }
    
    echo "\nПроверка после добавления:\n";
    echo "==========================\n";
    
    $preview1 = $textModel->getTextByKey('preview_message_1');
    $preview2 = $textModel->getTextByKey('preview_message_2');
    
    echo "preview_message_1: " . ($preview1 ? $preview1['text'] : 'НЕ НАЙДЕН') . "\n";
    echo "preview_message_2: " . ($preview2 ? $preview2['text'] : 'НЕ НАЙДЕН') . "\n";
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
}
?>

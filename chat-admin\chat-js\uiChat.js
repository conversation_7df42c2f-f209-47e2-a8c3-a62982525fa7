// --- START OF FILE uiChat.js ---

// chat-js/uiChat.js
import { findOrFail, findAll, createElement, truncateText, showNotificationAbove } from './domUtils.js';
import { SESSION_TITLE_MAX_LENGTH } from './config.js';
import { getState, setState } from './state.js';
import { SpeechRecognizer } from './speechRecognizer.js'; // Импортируем распознаватель
import { formatMarkdown } from './markdownFormatter.js'; // Импортируем наш форматер
import textManager from './textManager.js'; // Импортируем менеджер текстов

// --- Утилиты ---

/**
 * Извлекает чистый текст из элемента с markdown форматированием
 * Правильно обрабатывает <br> теги, списки, заголовки и другие элементы
 */
function extractCleanTextFromMarkdown(element) {
    if (!element) return '';

    // Клонируем элемент, чтобы не изменять оригинал
    const clone = element.cloneNode(true);

    // Заменяем <br> на переносы строк
    const brElements = clone.querySelectorAll('br');
    brElements.forEach(br => {
        br.replaceWith('\n');
    });

    // Заменяем блочные элементы на переносы строк
    const blockElements = clone.querySelectorAll('div, p, h1, h2, h3, h4, h5, h6, li, blockquote');
    blockElements.forEach(block => {
        // Добавляем перенос строки после блочных элементов
        if (block.nextSibling) {
            block.insertAdjacentText('afterend', '\n');
        }
    });

    // Заменяем списки на текст с маркерами
    const listItems = clone.querySelectorAll('li');
    listItems.forEach((li, index) => {
        const isOrderedList = li.closest('ol');
        const marker = isOrderedList ? `${index + 1}. ` : '• ';
        li.insertAdjacentText('afterbegin', marker);
    });

    // Получаем чистый текст
    let text = clone.innerText || clone.textContent || '';

    // Убираем лишние переносы строк
    text = text.replace(/\n{3,}/g, '\n\n').trim();

    return text;
}

// --- DOM Элементы (получаем один раз при инициализации) ---
let chatContainer, chatButton, chatWindow, chatCloseBtn, chatMessages, chatInput, chatSendBtn, typingIndicator, newChatBtn, chatSessionsList, micButton, micHint;

// Переменные для голосового ввода в текстовом чате
let simpleSpeechRecognizer = null;
let isMicListening = false;
let sendTimeout = null; // Таймер для автоотправки
const AUTO_SEND_DELAY = 3000; // Задержка автоотправки в мс (3 секунды)

// --- Инициализация UI ---
export function initChatUI(onSendMessage, onOpenSession, onCreateSession, onDeleteSession, onUpdateSessionTitle, onEditMessage, onCopyMessage) {
    try {
        // Используем классы для выбора элементов
        chatContainer = findOrFail('.czn-chat-button-wrapper');
        chatButton = findOrFail('.czn-chat-button'); // Кнопка для стилей
        chatWindow = findOrFail('.czn-chat-window');
        chatCloseBtn = findOrFail('.czn-chat-close-btn');
        chatMessages = findOrFail('.czn-chat-messages');
        chatInput = findOrFail('.czn-chat-window .czn-chat-input');
        chatSendBtn = findOrFail('.czn-chat-send-btn');
        typingIndicator = findOrFail('.czn-chat-typing-indicator');
        newChatBtn = findOrFail('.czn-chat-new-chat-btn');
        chatSessionsList = findOrFail('.czn-chat-sessions-list');
        micButton = findOrFail('.czn-chat-mic-button');
        micHint = findOrFail('.czn-chat-mic-hint');

        // Устанавливаем тексты из textManager
        chatInput.placeholder = textManager.getText('message_placeholder');
        micButton.title = textManager.getText('voice_input_title');
        chatSendBtn.title = textManager.getText('send_button_title');
        newChatBtn.title = textManager.getText('new_chat_title');
        chatCloseBtn.title = textManager.getText('close_chat_title');

        // Устанавливаем заголовок чата
        const chatTitle = findOrFail('.czn-chat-title');
        chatTitle.textContent = textManager.getText('chat_title');

        // Устанавливаем подсказку чата
        const chatHint = findOrFail('.czn-chat-hint');
        chatHint.textContent = textManager.getText('assistant_hint');

        // Устанавливаем текст подсказки микрофона
        micHint.textContent = textManager.getText('mic_hint');

        // --- Основные обработчики событий ---
        chatContainer.addEventListener('click', (event) => {
             // Предотвращаем открытие/закрытие чата при клике на кнопку звонка внутри контейнера
             if (event.target.closest('.czn-chat-call-ai-btn')) {
                 return;
             }
             toggleChatWindow();
        });

        chatCloseBtn.addEventListener('click', closeChatWindow);
        chatSendBtn.addEventListener('click', () => {
            if (chatSendBtn.disabled) return;
            // Передаем onSendMessage как колбэк
            handleSendClick(onSendMessage);
        });
        chatInput.addEventListener('keypress', (e) => {
            if (chatSendBtn.disabled) return;
            // Передаем onSendMessage как колбэк
            handleInputKeypress(e, onSendMessage);
        });
        newChatBtn.addEventListener('click', (e) => {
            e.preventDefault();
            onCreateSession();
        });

        // Делегирование событий для сообщений и списка сессий
        chatMessages.addEventListener('click', (e) => handleMessagesClick(e, onEditMessage, onCopyMessage));
        // Передаем onUpdateSessionTitle в обработчики списка
        chatSessionsList.addEventListener('click', (e) => handleSessionListClick(e, onOpenSession, onDeleteSession, onUpdateSessionTitle));
        chatSessionsList.addEventListener('keydown', (e) => handleSessionListKeydown(e, onUpdateSessionTitle)); // Передаем onUpdateSessionTitle
        chatSessionsList.addEventListener('focusout', (e) => handleSessionListFocusOut(e, onUpdateSessionTitle)); // Передаем onUpdateSessionTitle

        // Закрытие по клику вне окна
        document.addEventListener('click', handleDocumentClick);
        // Обработчик для кнопки микрофона в текстовом чате
        micButton.addEventListener('click', () => handleMicButtonClick(onSendMessage)); // Передаем onSendMessage
        chatInput.addEventListener('input', handleChatInputResize); // Обработчик для ресайза textarea

        console.log("Chat UI Initialized (with prefixed classes, container click enabled)");

    } catch (error) {
        console.error("Failed to initialize Chat UI:", error);
    }
}

// --- Функции управления UI ---

export function toggleChatWindow() {
    const { isChatOpen } = getState();
    setState({ isChatOpen: !isChatOpen });
    chatWindow.classList.toggle('czn-chat-active', !isChatOpen); // Используем префикс
    if (!isChatOpen) { // Если только что открыли
        chatInput.focus();
        scrollToBottom();

        // Превью проверяется асинхронно через setTimeout ниже
    } else { // Если закрыли
        cancelActiveEdit(); // Отменяем редактирование при закрытии
        stopSimpleRecognition(); // Останавливаем распознавание при закрытии
    }
}

export function openChatWindow() {
    if (getState().isChatOpen) return;
    setState({ isChatOpen: true });
    chatWindow.classList.add('czn-chat-active'); // Используем префикс
    chatInput.focus();
    scrollToBottom();

    // Небольшая задержка, чтобы DOM успел обновиться, затем проверяем превью
    setTimeout(async () => {
        try {
            console.log("UIChat: openChatWindow - checking for preview after 200ms delay");
            console.log("UIChat: openChatWindow - current DOM messages:", chatMessages ? chatMessages.children.length : 0);
            await checkAndShowPreviewForEmptyChat();
        } catch (error) {
            console.error("UIChat: Error in openChatWindow preview check:", error);
        }
    }, 200);
}

// Функция для принудительного показа превью (для тестирования и особых случаев)
// УДАЛЕНА - теперь используется только checkAndShowPreviewForEmptyChat с проверками

export function closeChatWindow() {
    if (!getState().isChatOpen) return;
    cancelActiveEdit(); // Отменяем редактирование при закрытии
    stopSimpleRecognition(); // Останавливаем распознавание при закрытии
    setState({ isChatOpen: false });
    chatWindow.classList.remove('czn-chat-active'); // Используем префикс
}

export function showTypingIndicator(show) {
    setState({ isTyping: show });
    typingIndicator.style.display = show ? 'flex' : 'none';
    if (show) {
        scrollToBottom();
    }
}

export function clearChatInput() {
    chatInput.value = '';
    // Сброс высоты textarea к минимальной после отправки
    chatInput.style.height = '40px';
    chatInput.style.overflowY = 'hidden';
    chatInput.style.resize = 'none';
    chatInput.scrollTop = 0;
    // Восстанавливаем плейсхолдер
    chatInput.placeholder = textManager.getText('message_placeholder');
    // Также вызываем ресайз, чтобы он сам установил правильную высоту после очистки
    handleChatInputResize();
}

export function focusChatInput() {
    chatInput.focus();
}

export function handleChatInputResize() {
    if (!chatInput) return;
    // Если пользователь вручную изменил размер (resize), не трогаем высоту
    if (chatInput.style.height && chatInput.style.height !== 'auto' && chatInput.style.height !== '40px' && chatInput.scrollHeight >= 300 && chatInput.style.resize === 'vertical') {
        return;
    }
    chatInput.style.height = 'auto'; // Сначала сбрасываем высоту
    if (chatInput.scrollHeight < 300) { // Если контент меньше максимума
        // Устанавливаем высоту не меньше минимальной (например, 40px)
        chatInput.style.height = Math.max(40, chatInput.scrollHeight) + 'px';
        chatInput.style.overflowY = 'hidden'; // Скрываем скроллбар
        chatInput.style.resize = 'none'; // Запрещаем ручное изменение
    } else { // Если контент больше или равен максимуму
        chatInput.style.height = '300px'; // Фиксируем максимальную высоту
        chatInput.style.overflowY = 'auto'; // Показываем скроллбар
        chatInput.style.resize = 'vertical'; // Разрешаем ручное изменение
    }
}

export function disableSendButton(disable) {
    chatSendBtn.disabled = disable;
    chatSendBtn.classList.toggle('czn-chat-pending-ansv', disable); // Используем префикс
}

export function clearMessages() {
    if (chatMessages) {
        chatMessages.innerHTML = '';
    }
}

// Очищаем системные сообщения (новый чат создан, чат пуст и т.д.)
function clearSystemMessages() {
    if (!chatMessages) return;

    const systemMessages = chatMessages.querySelectorAll('.czn-chat-message');
    systemMessages.forEach(message => {
        const content = message.querySelector('.czn-chat-message-content');
        if (content) {
            const text = content.innerText.toLowerCase();
            // Удаляем сообщения с системными текстами
            if (text.includes('чат пуст') ||
                text.includes('новый чат') ||
                text.includes('начните общение') ||
                text.includes('создан')) {
                console.log("UIChat: Removing system message:", text);
                message.remove();
            }
        }
    });
}

// ФУНКЦИЯ ПРЕВЬЮ БЕЗ СОХРАНЕНИЯ В БД
export async function checkAndShowPreviewForEmptyChat() {
    console.log("UIChat: checkAndShowPreviewForEmptyChat called!");
    console.log("UIChat: Call stack:", new Error().stack);

    try {
        // Проверяем, есть ли уже 2 превью сообщения в DOM (предотвращаем дублирование)
        const existingPreviewMessages = chatMessages ? chatMessages.querySelectorAll('.czn-chat-message-preview') : [];
        console.log("UIChat: Existing preview messages in DOM:", existingPreviewMessages.length);

        if (existingPreviewMessages.length >= 2) {
            console.log("UIChat: Already have 2 preview messages, skipping");
            return;
        }

        const currentState = getState();
        const messageHistory = currentState.messageHistory || [];

        console.log("UIChat: Current state:", {
            messageHistory: messageHistory.length,
            currentSessionId: currentState.currentSessionId,
            chatSessions: currentState.chatSessions ? currentState.chatSessions.length : 0,
            isChatOpen: currentState.isChatOpen
        });

        // Проверяем НЕ-превью сообщения в DOM
        const nonPreviewMessages = chatMessages ? chatMessages.querySelectorAll('.czn-chat-message:not(.czn-chat-message-preview)') : [];
        console.log("UIChat: Non-preview DOM messages count:", nonPreviewMessages.length);

        // Подробное логирование для отладки
        if (nonPreviewMessages.length > 0) {
            console.log("UIChat: Found non-preview messages in DOM:");
            nonPreviewMessages.forEach((msg, index) => {
                console.log(`  ${index + 1}. Classes: ${msg.className}, Text: ${msg.textContent.substring(0, 50)}...`);
            });
        }

        if (messageHistory.length > 0) {
            console.log("UIChat: Message history details:");
            messageHistory.forEach((msg, index) => {
                console.log(`  ${index + 1}. Role: ${msg.role}, Text: ${msg.content ? msg.content.substring(0, 50) : 'no content'}...`);
            });
        }

        // Если есть сообщения в истории ИЛИ НЕ-превью сообщения в DOM - не показываем превью
        if (messageHistory.length > 0 || nonPreviewMessages.length > 0) {
            console.log("UIChat: ❌ BLOCKING PREVIEW - Has messages in history or non-preview DOM messages");
            console.log("UIChat: Reason - messageHistory.length:", messageHistory.length, "nonPreviewMessages.length:", nonPreviewMessages.length);
            return;
        }

        console.log("UIChat: ✅ SHOWING PREVIEW - Empty chat (both history and DOM)");

        // Получаем настройки превью
        const { getSettings } = await import('./api.js');
        const settingsResponse = await getSettings();

        if (!settingsResponse?.settings) {
            console.log("UIChat: No settings available for preview");
            return;
        }

        const settings = settingsResponse.settings;
        const previewMessage1 = settings.preview_message_1;
        const previewMessage2 = settings.preview_message_2;

        console.log("UIChat: Preview settings:", {
            message1: previewMessage1 ? "configured" : "empty",
            message2: previewMessage2 ? "configured" : "empty"
        });

        // Если нет превью сообщений в настройках - не показываем
        if (!previewMessage1 && !previewMessage2) {
            console.log("UIChat: No preview messages configured");
            return;
        }

        // Показываем первое превью сообщение
        if (previewMessage1) {
            showTypingIndicator(true);

            setTimeout(async () => {
                try {
                    // Дополнительная проверка в DOM перед добавлением первого превью
                    const currentPreviewMessages = chatMessages ?
                        chatMessages.querySelectorAll('.czn-chat-message-preview') : [];

                    if (currentPreviewMessages.length >= 2) {
                        console.log("UIChat: Already have 2 preview messages in DOM, skipping first preview");
                        showTypingIndicator(false);
                        return;
                    }

                    showTypingIndicator(false);

                    // Создаем временный ID для первого превью сообщения
                    const tempId1 = `preview_${Date.now()}_1`;

                    // Добавляем превью сообщение в DOM с специальным флагом (НЕ сохраняем в базу)
                    addChatMessageElement("assistant", previewMessage1, tempId1, true); // true = isPreview

                    console.log("UIChat: Preview message 1 shown (not saved to database)");

                    if (previewMessage2) {
                        showTypingIndicator(true);
                    }
                } catch (error) {
                    console.error("UIChat: Error showing preview message 1:", error);
                }
            }, 3000);
        }

        // Показываем второе превью сообщение
        if (previewMessage2) {
            setTimeout(async () => {
                try {
                    // Дополнительная проверка в DOM перед добавлением второго превью
                    const currentPreviewMessages = chatMessages ?
                        chatMessages.querySelectorAll('.czn-chat-message-preview') : [];

                    if (currentPreviewMessages.length >= 2) {
                        console.log("UIChat: Already have 2 preview messages in DOM, skipping second preview");
                        showTypingIndicator(false);
                        return;
                    }

                    showTypingIndicator(false);

                    // Создаем временный ID для второго превью сообщения
                    const tempId2 = `preview_${Date.now()}_2`;

                    // Добавляем превью сообщение в DOM с специальным флагом (НЕ сохраняем в базу)
                    addChatMessageElement("assistant", previewMessage2, tempId2, true); // true = isPreview

                    console.log("UIChat: Preview message 2 shown (not saved to database)");
                    console.log("UIChat: All preview messages shown (not saved to database)!");
                } catch (error) {
                    console.error("UIChat: Error showing preview message 2:", error);
                }
            }, 6000);
        }

    } catch (error) {
        console.error("UIChat: Error in preview:", error);
    }
}

// УДАЛЕНА ФУНКЦИЯ showPreviewOnOpen() - она показывала превью без проверок
// Теперь используется только checkAndShowPreviewForEmptyChat() с полными проверками

// --- Функции работы с сообщениями ---
export function addChatMessageElement(sender, text, messageId, isPreview = false) {
    // Check if message with same ID already exists
    if (messageId && chatMessages) {
        const existingMessage = chatMessages.querySelector(`.czn-chat-message[data-id="${messageId}"]`);
        if (existingMessage) {
            console.log(`Message ${messageId} already exists, skipping duplicate`);
            return existingMessage;
        }
    }

    const attrs = {
        className: `czn-chat-message czn-chat-message-${sender}`, // Prefixed classes
        dataset: {}
    };

    // Добавляем специальный класс для превью сообщений
    if (isPreview) {
        attrs.className += ' czn-chat-message-preview';
        attrs.dataset.preview = 'true';
    }

    if (messageId !== undefined && messageId !== null && messageId !== '') {
        attrs.dataset.id = messageId;
    }
    if (sender === 'assistant') {
        attrs.dataset.rawText = text; // Store initial raw text
    }
    const messageElement = createElement('div', attrs);

    const messageBody = createElement('div', { className: 'czn-chat-message-body' }); // Prefixed
    const messageContent = createElement('div', { className: 'czn-chat-message-content' }); // Prefixed

    // Используем formatMarkdown всегда для единообразия, он обработает и простой текст
    messageContent.innerHTML = formatMarkdown(text);

    messageBody.appendChild(messageContent);

    // Для ассистента можно оставить место для доп. ответа, если нужно
    if (sender === 'assistant') {
        const assistantReply = createElement('div', { className: 'czn-chat-assistant-reply' }); // Prefixed
        messageBody.appendChild(assistantReply);
    }

    const messageFooter = createElement('div', { className: 'czn-chat-message-footer' }); // Prefixed
    const actionsContainer = createElement('div', { className: 'czn-chat-message-actions' }); // Prefixed

    const copyBtn = createElement('button', {
        className: 'czn-chat-copy-message-btn', // Prefixed
        title: 'Копировать'
    }, '<i class="fas fa-copy"></i>');
    actionsContainer.appendChild(copyBtn);

    if (sender === 'user') {
        const editBtn = createElement('button', {
            className: 'czn-chat-edit-message-btn', // Prefixed
            title: 'Редактировать'
        }, '<i class="fas fa-pencil-alt"></i>');
        actionsContainer.appendChild(editBtn);
    }

    const timeElement = createElement('span', { className: 'czn-chat-message-time' }, // Prefixed
        new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    );

    messageFooter.appendChild(actionsContainer);
    messageFooter.appendChild(timeElement);

    messageElement.appendChild(messageBody);
    messageElement.appendChild(messageFooter);

    if (chatMessages) {
        chatMessages.appendChild(messageElement);
        scrollToBottom(); // Прокрутка после добавления
    }

    return messageElement; // Возвращаем созданный элемент
}

// Логика тайпинга без изменений - убрана для краткости, так как не относится к проблеме
// let currentTypingAnimation = null;
// function appendChatTypingEffect(messageContentElement, text, speed = 30) { ... }

export function appendChatMessageContent(messageElement, textChunk, isFinalChunk = false) {
    if (!messageElement) {
        console.warn("uiChat.appendChatMessageContent: messageElement is null or undefined.");
        return;
    }

    if (messageElement.classList.contains('czn-chat-editing')) { // Prefixed
        const textarea = messageElement.querySelector('.czn-chat-message-edit-textarea'); // Prefixed
        if (textarea) {
            textarea.value += textChunk;
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
            scrollToBottom();
            return;
        }
    }

    const messageContent = messageElement.querySelector('.czn-chat-message-content'); // Prefixed
    if (messageContent) {
        if (messageElement.classList.contains('czn-chat-message-assistant')) { // Prefixed
            let rawText = messageElement.dataset.rawText || '';
            rawText += textChunk;
            messageElement.dataset.rawText = rawText;

            messageContent.classList.add('czn-chat-updating'); // Prefixed
            setTimeout(() => messageContent.classList.remove('czn-chat-updating'), 100); // Prefixed

            // Применяем Markdown форматирование
            messageContent.innerHTML = formatMarkdown(rawText);

        } else {
            // Для пользовательских сообщений (если вдруг понадобится) просто добавляем текст
            // Но formatMarkdown безопасен и для простого текста
             messageContent.innerHTML = formatMarkdown((messageContent.innerText || '') + textChunk);
        }
        // Прокрутка нужна всегда при добавлении контента
        scrollToBottom();
    } else {
        console.warn("uiChat.appendChatMessageContent: Could not find .czn-chat-message-content within", messageElement);
    }
}


export function updateChatMessageElementId(tempId, newId) {
    console.log(`uiChat: Attempting to update element ID from ${tempId} to ${newId}`);
    const messageElement = chatMessages?.querySelector(`[data-id="${tempId}"]`);
    if (messageElement) {
        messageElement.dataset.id = newId;
        console.log(`uiChat: Successfully updated element data-id for ${newId}`);
    } else {
        console.warn(`uiChat: Could not find element with tempId ${tempId} to update ID.`);
    }
}

export function removeMessagesAfterElement(messageElement) {
    let nextSibling = messageElement?.nextElementSibling;
    while (nextSibling) {
        const toRemove = nextSibling;
        nextSibling = nextSibling.nextElementSibling;
        toRemove.remove();
    }
}

export function scrollToBottom() {
    if (chatMessages) {
        // Небольшая задержка может помочь, если контент добавляется асинхронно
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 50);
    }
}


// --- Управление списком сессий ---

export function renderSessionList(sessions, currentSessionId) {
    if (!chatSessionsList) return;
    chatSessionsList.innerHTML = ''; // Очищаем

    if (Array.isArray(sessions) && sessions.length > 0) {
        const sessionsHeader = createElement('div', { className: 'czn-chat-sessions-header' }, // Prefixed
            createElement('a', { href: '#', className: 'czn-chat-sessions-toggle-link' }, [ // Prefixed
                createElement('span', {}, 'Мои чаты'),
                createElement('i', { className: 'fas fa-chevron-down' })
            ])
        );
        const sessionsContainer = createElement('div', { className: 'czn-chat-sessions-container' }); // Prefixed

        sessionsHeader.querySelector('.czn-chat-sessions-toggle-link').addEventListener('click', (e) => {
            e.preventDefault();
            toggleSessionsListUI(e.currentTarget);
        });
        chatSessionsList.appendChild(sessionsHeader);

        sessions.forEach(session => {
            const truncatedTitle = truncateText(session.title || `Чат #${session.id}`, SESSION_TITLE_MAX_LENGTH);
            const sessionItemDiv = createElement('div', {
                // Используем String() для сравнения ID сессии
                className: `czn-chat-session-item ${String(session.id) === String(currentSessionId) ? 'czn-chat-active' : ''}`, // Prefixed classes & string comparison
                dataset: { id: session.id }
            }, [
                createElement('span', { className: 'czn-chat-session-name', title: session.title || `Чат #${session.id}` }, truncatedTitle), // Prefixed
                createElement('input', { type: 'text', className: 'czn-chat-session-edit-input', value: session.title || `Чат #${session.id}` }), // Prefixed
                createElement('button', { className: 'czn-chat-edit-session-btn', title: 'Редактировать название' }, '<i class="fas fa-pencil-alt"></i>'), // Prefixed
                createElement('button', { className: 'czn-chat-delete-session-btn', title: 'Удалить чат' }, '<i class="fas fa-trash"></i>') // Prefixed
            ]);
            const li = createElement('li', {}, sessionItemDiv);
            sessionsContainer.appendChild(li);
        });
        chatSessionsList.appendChild(sessionsContainer);
        chatSessionsList.style.display = 'block'; // Показываем список, если есть сессии
    } else {
        chatSessionsList.style.display = 'none'; // Скрываем список, если сессий нет
    }
}


function toggleSessionsListUI(toggleLink) {
    const container = toggleLink.closest('.czn-chat-sessions-header')?.nextElementSibling; // Prefixed
    const icon = toggleLink.querySelector('i');
    if (container && icon && container.classList.contains('czn-chat-sessions-container')) { // Check class // Prefixed
        container.classList.toggle('czn-chat-active'); // Prefixed
        icon.classList.toggle('fa-chevron-down', !container.classList.contains('czn-chat-active')); // Prefixed
        icon.classList.toggle('fa-chevron-up', container.classList.contains('czn-chat-active')); // Prefixed
     }
 }

 export function highlightActiveSession(sessionId) {
     console.log(`uiChat: Highlighting session ${sessionId}`);
     const items = findAll('.czn-chat-session-item', chatSessionsList); // Prefixed
     console.log(`uiChat: Found ${items.length} session items to check.`);
     let highlighted = false;
     items.forEach(item => {
         // Сравниваем как строки для надежности
         const isActive = String(item.dataset.id) === String(sessionId);
         item.classList.toggle('czn-chat-active', isActive); // Prefixed
         if (isActive) {
             highlighted = true;
             console.log(`uiChat: Applied 'czn-chat-active' class to item with data-id ${item.dataset.id}`);
         }
     });
     if (!highlighted) {
         console.warn(`uiChat: No session item found with data-id ${sessionId} to highlight.`);
     }
}

// --- Редактирование сообщений ---

function startEditingMessage(messageElement) {
    const { activeEditingMessageId } = getState();
    if (activeEditingMessageId) {
        cancelActiveEdit();
    }

    const messageId = messageElement.dataset.id;
    const messageContentElement = messageElement.querySelector('.czn-chat-message-content'); // Prefixed
    if (!messageContentElement) return;

    // Получаем текст, как он есть (может содержать <br>)
    // Лучше взять innerText для редактирования
    const currentContent = messageContentElement.innerText; // Используем innerText
    messageElement.dataset.originalContent = currentContent;

    setState({ activeEditingMessageId: messageId });
    messageElement.classList.add('czn-chat-editing'); // Prefixed

    const messageBody = messageElement.querySelector('.czn-chat-message-body'); // Prefixed
    const messageFooter = messageElement.querySelector('.czn-chat-message-footer'); // Prefixed
    const actionsContainer = messageFooter.querySelector('.czn-chat-message-actions'); // Prefixed

    const textarea = createElement('textarea', { className: 'czn-chat-message-edit-textarea' }); // Prefixed
    textarea.value = currentContent; // Вставляем чистый текст

    const saveBtn = createElement('button', { className: 'czn-chat-save-edit-btn', title: 'Сохранить' }, '<i class="fas fa-check"></i>'); // Prefixed
    const cancelBtn = createElement('button', { className: 'czn-chat-cancel-edit-btn', title: 'Отмена' }, '<i class="fas fa-times"></i>'); // Prefixed

    messageBody.innerHTML = '';
    messageBody.appendChild(textarea);

    actionsContainer.innerHTML = '';
    actionsContainer.appendChild(saveBtn);
    actionsContainer.appendChild(cancelBtn);

    textarea.focus();
    textarea.style.height = 'auto';
    textarea.style.height = (textarea.scrollHeight) + 'px';
    textarea.addEventListener('input', () => {
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    });
}

function cancelActiveEdit() {
    const { activeEditingMessageId } = getState();
    if (!activeEditingMessageId) return;

    const messageElement = chatMessages?.querySelector(`.czn-chat-message.czn-chat-editing[data-id="${activeEditingMessageId}"]`); // Prefixed
    if (messageElement) {
        const originalContent = messageElement.dataset.originalContent || '';
        restoreOriginalMessageView(messageElement, originalContent);
    }
    setState({ activeEditingMessageId: null });
}

export function restoreOriginalMessageView(messageElement, content) {
    if (!messageElement) return;
    messageElement.classList.remove('czn-chat-editing'); // Prefixed
    delete messageElement.dataset.originalContent;

    const messageBody = messageElement.querySelector('.czn-chat-message-body'); // Prefixed
    const messageFooter = messageElement.querySelector('.czn-chat-message-footer'); // Prefixed
    const actionsContainer = messageFooter.querySelector('.czn-chat-message-actions'); // Prefixed
    const timeElement = messageFooter.querySelector('.czn-chat-message-time'); // Prefixed

    messageBody.innerHTML = '';
    const messageContent = createElement('div', { className: 'czn-chat-message-content' }); // Prefixed
    // При восстановлении используем formatMarkdown, он обработает переносы строк если надо
    messageContent.innerHTML = formatMarkdown(content);
    messageBody.appendChild(messageContent);

     if (messageElement.classList.contains('czn-chat-message-assistant')) { // Prefixed
        const assistantReply = createElement('div', { className: 'czn-chat-assistant-reply' }); // Prefixed
        messageBody.appendChild(assistantReply);
     }

    actionsContainer.innerHTML = '';
    const copyBtn = createElement('button', { className: 'czn-chat-copy-message-btn', title: 'Копировать' }, '<i class="fas fa-copy"></i>'); // Prefixed
    actionsContainer.appendChild(copyBtn);

    if (messageElement.classList.contains('czn-chat-message-user')) { // Prefixed
        const editBtn = createElement('button', { className: 'czn-chat-edit-message-btn', title: 'Редактировать' }, '<i class="fas fa-pencil-alt"></i>'); // Prefixed
        actionsContainer.appendChild(editBtn);
    }

    // Восстанавливаем или добавляем время
    if (timeElement && !messageFooter.contains(timeElement)) {
        messageFooter.appendChild(timeElement);
    } else if (!timeElement) {
        const newTimeElement = createElement('span', { className: 'czn-chat-message-time' }, // Prefixed
             new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        );
        messageFooter.appendChild(newTimeElement);
    }

    messageFooter.insertBefore(actionsContainer, messageFooter.firstChild); // Убедимся, что кнопки идут первыми
}

// --- Обработчики событий ---

function handleSendClick(onSendMessage) {
    console.log("handleSendClick called"); // DEBUG
    // Получаем элемент каждый раз, чтобы быть уверенным в его актуальности
    const currentChatInput = findOrFail('.czn-chat-window .czn-chat-input');
    const message = currentChatInput.value.trim();
    if (message) {
        console.log("Message found, calling onSendMessage:", message); // DEBUG
        onSendMessage(message); // Вызываем колбэк chatHandler'а
        clearChatInput(); // Очищаем поле ввода
    } else {
         console.log("No message to send"); // DEBUG
    }
}

function handleInputKeypress(event, onSendMessage) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSendClick(onSendMessage); // Вызываем колбэк chatHandler'а
    } else if (event.key === 'Enter' && event.shiftKey) {
        // Даем браузеру вставить перенос строки перед пересчетом высоты
        setTimeout(handleChatInputResize, 0);
    }
}

function handleMessagesClick(event, onEditMessage, onCopyMessage) {
    const messageElement = event.target.closest('.czn-chat-message'); // Prefixed
    if (!messageElement) return;

    const messageId = messageElement.dataset.id;
    console.log(`uiChat: Clicked message element, extracted messageId: ${messageId}`);

    const copyBtn = event.target.closest('.czn-chat-copy-message-btn'); // Prefixed
    if (copyBtn) {
        event.stopPropagation();
        const messageContentElement = messageElement.querySelector('.czn-chat-message-content'); // Prefixed
        // Используем улучшенную функцию для извлечения текста из markdown
        const textToCopy = messageContentElement ? extractCleanTextFromMarkdown(messageContentElement) : '';
        onCopyMessage(textToCopy, copyBtn, event);
        return;
    }

    const editBtn = event.target.closest('.czn-chat-edit-message-btn'); // Prefixed
    if (editBtn && messageElement.classList.contains('czn-chat-message-user')) { // Prefixed
        event.stopPropagation();
        startEditingMessage(messageElement);
        return;
    }

    const saveBtn = event.target.closest('.czn-chat-save-edit-btn'); // Prefixed
    if (saveBtn && messageElement.classList.contains('czn-chat-editing')) { // Prefixed
         event.stopPropagation();
         const textarea = messageElement.querySelector('.czn-chat-message-edit-textarea'); // Prefixed
         const newContent = textarea.value.trim();
         const originalContent = messageElement.dataset.originalContent || '';

         // Убираем режим редактирования *до* вызова колбэка
         restoreOriginalMessageView(messageElement, newContent || originalContent); // Восстанавливаем с новым (или старым, если пусто) контентом
         setState({ activeEditingMessageId: null });

         if (newContent && newContent !== originalContent) {
             if (!messageId || String(messageId).startsWith('temp_')) {
                 console.error(`uiChat: Attempted to save edit with invalid messageId: ${messageId}`);
                 alert('Невозможно сохранить редактирование: ID сообщения некорректен. Попробуйте обновить страницу.');
                 // Восстанавливаем старый контент в UI после ошибки
                 restoreOriginalMessageView(messageElement, originalContent);
             } else {
                 // Вызываем колбэк chatHandler'а для сохранения и получения нового ответа
                 onEditMessage(messageId, newContent, messageElement, event);
             }
         } else if (!newContent) {
             alert('Сообщение не может быть пустым.');
             // Уже восстановили вид с originalContent
         } else {
              console.log("Content not changed.");
             // Уже восстановили вид
         }
         return;
    }

    const cancelBtn = event.target.closest('.czn-chat-cancel-edit-btn'); // Prefixed
    if (cancelBtn && messageElement.classList.contains('czn-chat-editing')) { // Prefixed
         event.stopPropagation();
         const originalContent = messageElement.dataset.originalContent || '';
         restoreOriginalMessageView(messageElement, originalContent);
         setState({ activeEditingMessageId: null });
         return;
    }
}


function handleSessionListClick(event, onOpenSession, onDeleteSession, onUpdateSessionTitle) {
    const sessionItem = event.target.closest('.czn-chat-session-item'); // Prefixed
    if (!sessionItem) return;

    const sessionId = sessionItem.dataset.id;
    const sessionLi = sessionItem.closest('li');

    const editBtn = event.target.closest('.czn-chat-edit-session-btn'); // Prefixed
    if (editBtn) {
        event.stopPropagation();
        toggleSessionTitleEditUI(sessionItem, sessionId, onUpdateSessionTitle);
        return;
    }

    const deleteBtn = event.target.closest('.czn-chat-delete-session-btn'); // Prefixed
    if (deleteBtn) {
        event.stopPropagation();
        if (confirm('Вы уверены, что хотите удалить этот чат? Вся история будет потеряна.')) {
            onDeleteSession(sessionId, sessionLi);
        }
        return;
    }

    // Игнорируем клик по полю ввода
    if (event.target.classList.contains('czn-chat-session-edit-input')) { // Prefixed
        event.stopPropagation();
        return;
    }

    // Клик по самому элементу (не по кнопкам и не по инпуту)
    const currentSessionId = getState().currentSessionId;
    // Сравниваем как строки
    if (String(sessionId) !== String(currentSessionId)) {
         highlightActiveSession(sessionId);
         onOpenSession(sessionId); // Вызываем колбэк chatHandler'а
    }
}


function handleSessionListKeydown(event, onUpdateSessionTitle) { // Добавлен onUpdateSessionTitle
    if (event.key === 'Enter' && event.target.classList.contains('czn-chat-session-edit-input')) { // Prefixed
        event.preventDefault();
        const sessionItem = event.target.closest('.czn-chat-session-item'); // Prefixed
        const sessionId = sessionItem?.dataset.id;
        if (sessionItem && sessionId && sessionItem.classList.contains('czn-chat-editing')) { // Prefixed
            console.log("Enter pressed in session edit input. Finishing edit.");
            finishSessionTitleEdit(sessionItem, sessionId, onUpdateSessionTitle); // Прямой вызов
        }
    }
}


function handleSessionListFocusOut(event, onUpdateSessionTitle) { // Добавлен onUpdateSessionTitle
     if (event.target.classList.contains('czn-chat-session-edit-input')) { // Prefixed
         const sessionItem = event.target.closest('.czn-chat-session-item'); // Prefixed
         const sessionId = sessionItem?.dataset.id;
         // Небольшая задержка, чтобы проверить, не перешел ли фокус на кнопку сохранения/отмены
         setTimeout(() => {
             if (sessionItem && sessionId && sessionItem.classList.contains('czn-chat-editing')) { // Prefixed
                 // Проверяем, активный элемент все еще внутри sessionItem? (напр. кнопка)
                 if (!sessionItem.contains(document.activeElement)) {
                     console.log("Focus lost from session edit input and its buttons. Finishing edit.");
                     finishSessionTitleEdit(sessionItem, sessionId, onUpdateSessionTitle); // Прямой вызов
                 } else {
                      console.log("Focus moved within session item, not finishing edit on focus out.");
                 }
             }
         }, 100); // Увеличил задержку для надежности
     }
}


function toggleSessionTitleEditUI(sessionItem, sessionId, onUpdateSessionTitle) {
    const editInput = sessionItem.querySelector('.czn-chat-session-edit-input'); // Prefixed
    const editBtn = sessionItem.querySelector('.czn-chat-edit-session-btn'); // Prefixed
    const sessionName = sessionItem.querySelector('.czn-chat-session-name'); // Prefixed

    if (sessionItem.classList.contains('czn-chat-editing')) { // Prefixed
        console.log("Checkmark clicked. Finishing edit.");
        finishSessionTitleEdit(sessionItem, sessionId, onUpdateSessionTitle);
    } else {
        console.log("Pencil clicked. Starting edit.");
        // Закрываем редактирование других сессий, если они есть
        findAll('.czn-chat-session-item.czn-chat-editing', chatSessionsList).forEach(item => { // Prefixed
            if (item !== sessionItem) {
                 finishSessionTitleEdit(item, item.dataset.id, onUpdateSessionTitle);
            }
        });

        sessionItem.classList.add('czn-chat-editing'); // Prefixed
        editInput.value = sessionName.getAttribute('title'); // Берем полное название из title
        editInput.style.display = 'block';
        editInput.focus();
        editInput.select();
        editBtn.innerHTML = '<i class="fas fa-check"></i>';
        editBtn.title = 'Сохранить название';
        editBtn.classList.add('czn-chat-confirm'); // Prefixed
    }
}


function finishSessionTitleEdit(sessionItem, sessionId, onUpdateSessionTitle) {
    if (!sessionItem || !sessionItem.classList.contains('czn-chat-editing')) { // Prefixed
        console.log("finishSessionTitleEdit called, but not in editing mode or item invalid. Skipping.");
        return;
    }

    console.log(`Finishing edit for session ${sessionId}`);
    const editInput = sessionItem.querySelector('.czn-chat-session-edit-input'); // Prefixed
    const sessionName = sessionItem.querySelector('.czn-chat-session-name'); // Prefixed
    const editBtn = sessionItem.querySelector('.czn-chat-edit-session-btn'); // Prefixed

    const newTitle = editInput.value.trim();
    const oldTitle = sessionName.getAttribute('title'); // Берем старое полное название из title

    // Обновляем UI немедленно
    const truncatedNewTitle = truncateText(newTitle || oldTitle, SESSION_TITLE_MAX_LENGTH);
    sessionName.textContent = truncatedNewTitle;
    sessionName.setAttribute('title', newTitle || oldTitle); // Сохраняем полное название

    console.log(`Attempting to remove 'czn-chat-editing' from:`, sessionItem);
    sessionItem.classList.remove('czn-chat-editing'); // Prefixed
    console.log(`Class list after removal:`, sessionItem.classList);

    editInput.style.display = 'none';
    editBtn.innerHTML = '<i class="fas fa-pencil-alt"></i>';
    editBtn.title = 'Редактировать название';
    editBtn.classList.remove('czn-chat-confirm'); // Prefixed

    // Вызываем колбэк для обновления на сервере, если название изменилось и не пустое
    if (newTitle && newTitle !== oldTitle) {
        console.log(`Title changed from "${oldTitle}" to "${newTitle}". Calling update callback.`);
        onUpdateSessionTitle(sessionId, newTitle) // Вызываем колбэк chatHandler'а
            .then(() => {
                console.log(`Session ${sessionId} title updated successfully on server.`);
                // Показываем уведомление об успехе
                showNotificationAbove("Название чата обновлено!", editBtn);
            })
            .catch(error => {
                console.error(`Failed to save title for session ${sessionId}:`, error);
                // Откатываем UI к старому названию при ошибке
                sessionName.textContent = truncateText(oldTitle, SESSION_TITLE_MAX_LENGTH);
                sessionName.setAttribute('title', oldTitle);
                editInput.value = oldTitle; // Также откатываем значение в скрытом input
                alert(textManager.getText('error_update_chat_title') + error.message);
            });
    } else if (!newTitle) {
        console.log("New title is empty. Reverted.");
        // UI уже откатился при обновлении textContent/setAttribute выше
        alert(textManager.getText('error_chat_title_empty'));
    } else {
        console.log("Title was not changed.");
    }
}

// Обработчик клика вне окна чата
function handleDocumentClick(event) {
    const { isChatOpen } = getState();
    // Проверяем, открыт ли чат и был ли клик *вне* контейнера чата
    if (isChatOpen && !event.target.closest('.czn-chat-container-wrapper')) {
        // Проверяем, не кликнули ли внутри редактируемого сообщения
        const editingMessage = chatMessages?.querySelector('.czn-chat-message.czn-chat-editing'); // Prefixed
        if (!editingMessage || !editingMessage.contains(event.target)) {
             // Проверяем, не кликнули ли внутри уведомления копирования
             if (!event.target.closest('.czn-chat-copy-notification')) { // Prefixed
                 // И не кликнули ли внутри модального окна (если оно открыто)
                 if (!event.target.closest('.czn-chat-modal')) {
                      // И не кликнули ли внутри редактируемой сессии
                      const editingSession = chatSessionsList?.querySelector('.czn-chat-session-item.czn-chat-editing'); // Prefixed
                      if (!editingSession || !editingSession.contains(event.target)) {
                            closeChatWindow();
                      }
                 }
             }
        }
    }
}


// --- Логика голосового ввода в текстовом чате ---

let isProcessingMicClick = false; // Флаг для предотвращения двойных кликов по микрофону

function toggleMicrophoneUIState(enable) {
    // Используем переменные из области видимости модуля, обновленные в initChatUI
    if (enable) {
        micButton.classList.add('czn-chat-recording');
        if (micHint) {
            micHint.style.opacity = '1';
            micHint.style.visibility = 'visible';
            // Текст хинта микрофона остается прежним
            // micHint.textContent = textManager.getText('mic_hint');
        }
        // Но плейсхолдер меняется на краткий "Говорите"
        chatInput.placeholder = textManager.getText('mic_active_text');
    } else {
        micButton.classList.remove('czn-chat-recording');
        if (micHint) {
            micHint.style.opacity = '0';
            micHint.style.visibility = 'hidden';
        }
        // Восстанавливаем плейсхолдер
        chatInput.placeholder = textManager.getText('message_placeholder');
    }
}

function handleMicButtonClick(onSendMessage) {
    if (isProcessingMicClick) {
        console.log("Mic click processing, ignoring.");
        return;
    }
    isProcessingMicClick = true;
    console.log("handleMicButtonClick called, isMicListening:", isMicListening);

    // Пере-получаем элементы на всякий случай, если DOM изменился
    micButton = findOrFail('.czn-chat-mic-button');
    micHint = findOrFail('.czn-chat-mic-hint');
    chatInput = findOrFail('.czn-chat-window .czn-chat-input');


    if (!isMicListening) {
        // --- Активация микрофона ---
        console.log("Activating microphone...");
        try {
            if (!SpeechRecognizer.isSupported()) {
                alert(textManager.getText('error_speech_not_supported'));
                throw new Error("Speech recognition not supported");
            }

            cleanupSimpleRecognizer(); // Гарантируем чистое состояние перед стартом

            isMicListening = true; // Устанавливаем флаг *перед* созданием
            toggleMicrophoneUIState(true);

            simpleSpeechRecognizer = new SpeechRecognizer({
                onResult: (event) => handleSimpleRecognitionResult(event, onSendMessage),
                onError: (event) => handleSimpleRecognitionError(event), // Передаем event
                onEnd: () => handleSimpleRecognitionEnd(onSendMessage), // Убрали details, он не стандартный
                onStart: () => handleSimpleRecognitionStart()
            });

            console.log("Calling simpleSpeechRecognizer.start()...");
            simpleSpeechRecognizer.start(); // Это асинхронный вызов

        } catch (error) {
            console.error("Error starting microphone:", error);
            alert(textManager.getText('error_mic_activation') + error.message);
            // Очистка состояния при ошибке старта
            cleanupSimpleRecognizer(); // isMicListening сбросится внутри
            toggleMicrophoneUIState(false); // Убеждаемся, что UI обновлен
        } finally {
            isProcessingMicClick = false; // Снимаем блокировку
        }
    } else {
        // --- Деактивация микрофона ---
        console.log("Deactivating microphone (user clicked stop)...");
        stopSimpleRecognition(); // Останавливает распознавание и вызывает cleanup
        // UI и isMicListening будут обновлены внутри cleanupSimpleRecognizer
        isProcessingMicClick = false; // Снимаем блокировку
    }
}

function handleSimpleRecognitionStart() {
    // Этот колбэк вызывается, когда распознавание *фактически* началось
    console.log("Simple Mic: Recognition actually started (onStart).");
    // UI уже должен быть обновлен в handleMicButtonClick
    clearTimeout(sendTimeout); // Очищаем старый таймер, если он вдруг остался
    sendTimeout = null;
}

function handleSimpleRecognitionResult(event, onSendMessage) {
    console.groupCollapsed('Recognition Result'); // Свернутый лог по умолчанию
    let finalTranscript = '';
    let interimTranscript = '';
    // Получаем chatInput здесь, т.к. он мог измениться
    const currentChatInput = findOrFail('.czn-chat-window .czn-chat-input');

    try {
        if (!isMicListening || !event.results) {
            console.log('Skipping result: Mic not listening or no results.');
            return;
        }
        console.log('Received results object:', event);

        for (let i = event.resultIndex; i < event.results.length; ++i) {
            const result = event.results[i];
            if (result[0]) {
                const transcript = result[0].transcript;
                console.log(`Result ${i}: "${transcript}" (isFinal: ${result.isFinal})`);
                if (result.isFinal) {
                    finalTranscript += transcript.trim() + ' '; // Добавляем пробел после каждого финального сегмента
                } else {
                    interimTranscript += transcript; // Промежуточный - как есть
                }
            }
        }

        // Объединяем финальный и последний промежуточный результат
        // Финальный текст - это все финальные сегменты
        // Текст для отображения - финальный + текущий промежуточный
        const currentTextForInput = (finalTranscript + interimTranscript).trim();

        console.log(`Final: "${finalTranscript.trim()}" | Interim: "${interimTranscript.trim()}" -> Input: "${currentTextForInput}"`);

        // Обновляем поле ввода
        currentChatInput.value = currentTextForInput;
        handleChatInputResize(); // Подгоняем размер поля ввода

        // Сбрасываем и устанавливаем таймер автоотправки *только* если есть текст
        clearTimeout(sendTimeout);
        sendTimeout = null;

        if (currentTextForInput) {
            console.log(`Setting auto-send timer (${AUTO_SEND_DELAY}ms)`);
            sendTimeout = setTimeout(() => {
                console.log('%cAuto-send timer fired!', 'color: blue; font-weight: bold;');
                // Получаем самое актуальное значение перед отправкой
                const textToSend = findOrFail('.czn-chat-window .czn-chat-input').value.trim();

                // Отправляем, только если текст все еще есть и микрофон был активен *в момент срабатывания таймера*
                // (хотя stopSimpleRecognition должен был очистить таймер при ручной остановке)
                if (textToSend && isMicListening) {
                    console.log('Auto-sending message:', textToSend);
                    handleSendClick(onSendMessage); // Отправляем через стандартную функцию
                    // После успешной автоотправки останавливаем распознавание
                    stopSimpleRecognition();
                } else {
                    console.log('Auto-send skipped:', { textToSend, isMicListening });
                    // Если не отправили, но таймер сработал, все равно стоит остановить?
                    // Нет, возможно пользователь просто стер текст. Пусть продолжает слушать.
                    // Но если микрофон уже выключен, то ничего не делаем.
                }
                sendTimeout = null; // Таймер сработал, сбрасываем ID
            }, AUTO_SEND_DELAY); // Используем константу
        } else {
            console.log("No text detected, timer not set.");
        }

    } catch (error) {
        console.error("Error in handleSimpleRecognitionResult:", error);
    } finally {
        console.groupEnd();
    }
}


function handleSimpleRecognitionError(event) {
    console.error("Simple Mic Error:", event.error, event.message);
    let message = "Произошла ошибка распознавания речи.";
    // Стандартные коды ошибок Web Speech API
    switch (event.error) {
        case 'no-speech':
            message = "Речь не распознана. Попробуйте говорить громче или ближе к микрофону.";
            // В этом случае не показываем alert, просто прекращаем слушать
            break;
        case 'audio-capture':
            message = "Ошибка захвата звука. Проверьте подключение и настройки микрофона.";
            alert(message);
            break;
        case 'not-allowed':
            message = "Доступ к микрофону запрещен. Проверьте разрешения в настройках браузера.";
            alert(message);
            break;
        case 'service-not-allowed':
             message = "Доступ к сервису распознавания речи запрещен (возможно, из-за настроек браузера или системы).";
             alert(message);
            break;
        case 'network':
            message = "Сетевая ошибка при распознавании речи. Проверьте интернет-соединение.";
            alert(message);
            break;
        case 'aborted':
            message = "Распознавание прервано."; // Часто из-за вызова stop()
            console.log("Recognition aborted (likely intentional stop).");
            break; // Не показываем alert
        case 'bad-grammar':
             message = "Ошибка в грамматике запроса к сервису распознавания.";
             alert(message);
             break;
        case 'language-not-supported':
             message = "Выбранный язык не поддерживается.";
             alert(message);
             break;
        default:
            alert(message + ` (Код: ${event.error})`);
    }

    console.warn("Simple Mic Error Triggered Cleanup. Error:", event.error);
    cleanupSimpleRecognizer(); // Очищаем состояние при любой ошибке
    // Убеждаемся, что UI выключен после ошибки
    toggleMicrophoneUIState(false);
}

function handleSimpleRecognitionEnd(onSendMessage) {
    // Вызывается когда распознавание завершилось (успешно, по ошибке, или stop())
    console.log("Simple Mic: Recognition ended (onEnd). Current isMicListening:", isMicListening);

    // --- Принудительная автоотправка текста из textarea по завершению распознавания ---
    const chatInput = document.querySelector('.czn-chat-window .czn-chat-input');
    const textToSend = chatInput ? chatInput.value.trim() : '';
    if (textToSend) {
        console.log('Auto-send on recognition end:', textToSend);
        handleSendClick(onSendMessage);
    }

    // Основная задача onEnd - убедиться, что если распознавание закончилось *не* из-за
    // явного вызова stop() или ошибки, мы все равно корректно очищаем состояние.
    if (isMicListening) {
        // Если onEnd вызван, а мы все еще думаем, что слушаем, значит,
        // распознавание завершилось само (например, 'no-speech' без явной ошибки onError,
        // или просто достигнут лимит времени).
        console.log("Simple Mic: onEnd called while isMicListening was true. Performing cleanup.");
        cleanupSimpleRecognizer();
        toggleMicrophoneUIState(false); // Убедиться, что UI выключен
    } else {
         console.log("Simple Mic: onEnd called, isMicListening is already false. Cleanup likely done.");
    }
    // Дополнительно очищаем таймер на всякий случай
    clearTimeout(sendTimeout);
    sendTimeout = null;
}

// Функция для остановки распознавания (например, по кнопке)
function stopSimpleRecognition() {
    console.log("stopSimpleRecognition called...");
    clearTimeout(sendTimeout); // 1. Отменяем таймер автоотправки
    sendTimeout = null;

    if (simpleSpeechRecognizer && isMicListening) {
        console.log("Calling recognizer.stop()...");
        try {
             simpleSpeechRecognizer.stop(); // 2. Говорим API остановить запись/распознавание
             // Вызов stop() асинхронно вызовет событие onEnd.
             // isMicListening и UI будут сброшены в cleanupSimpleRecognizer,
             // который вызывается из onEnd или onError('aborted').
        } catch (e) {
             console.warn("Error calling recognizer.stop():", e.message, "- forcing cleanup.");
             // Если stop() вызвал ошибку, принудительно чистим
             cleanupSimpleRecognizer();
             toggleMicrophoneUIState(false);
        }
    } else {
        // Если распознавателя нет или он уже не слушает, просто убедимся, что все чисто.
        console.log("Recognizer not active or doesn't exist. Ensuring cleanup state.");
        cleanupSimpleRecognizer(); // Вызовет сброс isMicListening
        toggleMicrophoneUIState(false); // И обновит UI
    }
    console.log("stopSimpleRecognition finished.");
}

// Функция для полной очистки состояния и ресурсов распознавания
function cleanupSimpleRecognizer() {
    console.groupCollapsed("Cleanup Recognizer"); // Свернутый лог
    console.log("Running cleanup... Current isMicListening:", isMicListening);

    // 1. Останавливаем таймер
    clearTimeout(sendTimeout);
    sendTimeout = null;
    console.log("Timeout cleared.");

    // 2. Уничтожаем объект распознавателя
    if (simpleSpeechRecognizer) {
        console.log("Destroying SpeechRecognizer instance...");
        try {
            // Явно отписываемся от событий перед destroy, чтобы избежать гонок состояний
            if (simpleSpeechRecognizer.recognition) {
                 simpleSpeechRecognizer.recognition.onstart = null;
                 simpleSpeechRecognizer.recognition.onresult = null;
                 simpleSpeechRecognizer.recognition.onerror = null;
                 simpleSpeechRecognizer.recognition.onend = null;
                 console.log("Event listeners detached.");
                 // Некоторые реализации могут требовать abort() перед удалением
                 // simpleSpeechRecognizer.recognition.abort();
            }
            simpleSpeechRecognizer.destroy(); // Вызываем метод нашего класса
            console.log("Recognizer destroyed.");
        } catch (e) {
            console.error("Error during recognizer destroy:", e);
        } finally {
            simpleSpeechRecognizer = null; // Удаляем ссылку
        }
    } else {
        console.log("No recognizer instance to destroy.");
    }

    // 3. Сбрасываем флаг состояния
    if (isMicListening) {
        isMicListening = false;
        console.log("isMicListening set to false.");
    } else {
        console.log("isMicListening was already false.");
    }

    // 4. Сброс UI выполняется отдельно в вызывающих функциях (handleMicButtonClick, onError, onEnd)
    // с помощью toggleMicrophoneUIState(false), т.к. cleanup может быть вызван из разных мест.
    // Но на всякий случай, можно добавить обновление плейсхолдера здесь:
    const currentChatInput = document.querySelector('.czn-chat-window .czn-chat-input');
    if (currentChatInput && currentChatInput.placeholder === 'Говорите...') {
         currentChatInput.placeholder = 'Введите сообщение...';
         console.log("Placeholder reset (in cleanup).");
    }


    console.log("Cleanup finished.");
    console.groupEnd();
}


// --- END OF FILE uiChat.js ---